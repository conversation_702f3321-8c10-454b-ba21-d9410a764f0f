export declare function getMouseEventOptions(event: string, init?: MouseEventInit, clickCount?: number): {
    detail: number;
    buttons: number;
    button: number;
    clientX?: number | undefined;
    clientY?: number | undefined;
    movementX?: number | undefined;
    movementY?: number | undefined;
    relatedTarget?: EventTarget | null | undefined;
    screenX?: number | undefined;
    screenY?: number | undefined;
    altKey?: boolean | undefined;
    ctrlKey?: boolean | undefined;
    metaKey?: boolean | undefined;
    modifierAltGraph?: boolean | undefined;
    modifierCapsLock?: boolean | undefined;
    modifierFn?: boolean | undefined;
    modifierFnLock?: boolean | undefined;
    modifierHyper?: boolean | undefined;
    modifierNumLock?: boolean | undefined;
    modifierScrollLock?: boolean | undefined;
    modifierSuper?: boolean | undefined;
    modifierSymbol?: boolean | undefined;
    modifierSymbolLock?: boolean | undefined;
    shiftKey?: boolean | undefined;
    view?: Window | null | undefined;
    which?: number | undefined;
    bubbles?: boolean | undefined;
    cancelable?: boolean | undefined;
    composed?: boolean | undefined;
};
