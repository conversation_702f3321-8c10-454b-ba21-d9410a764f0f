{"_from": "@testing-library/dom@^10.4.0", "_id": "@testing-library/dom@10.4.0", "_inBundle": false, "_integrity": "sha512-pemlzrSESWbdAloYml3bAJMEfNh1Z7EduzqPKprCH5S341frlpYnUEW0H72dLxa6IsYr+mPno20GiSm+h9dEdQ==", "_location": "/@testing-library/dom", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@testing-library/dom@^10.4.0", "name": "@testing-library/dom", "escapedName": "@testing-library%2fdom", "scope": "@testing-library", "rawSpec": "^10.4.0", "saveSpec": null, "fetchSpec": "^10.4.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@testing-library/dom/-/dom-10.4.0.tgz", "_shasum": "82a9d9462f11d240ecadbf406607c6ceeeff43a8", "_spec": "@testing-library/dom@^10.4.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client", "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "https://kentcdodds.com"}, "browserslist": ["and_chr 103", "and_ff 101", "and_qq 10.4", "and_uc 12.12", "android 103", "chrome 102", "edge 102", "firefox 91", "ios_saf 12.2-12.5", "kaios 2.5", "op_mini all", "op_mob 64", "opera 88", "safari 15.5", "samsung 17.0", "samsung 16.0", "node 18.0"], "bugs": {"url": "https://github.com/testing-library/dom-testing-library/issues"}, "bundleDependencies": false, "dependencies": {"@babel/code-frame": "^7.10.4", "@babel/runtime": "^7.12.5", "@types/aria-query": "^5.0.1", "aria-query": "5.3.0", "chalk": "^4.1.0", "dom-accessibility-api": "^0.5.9", "lz-string": "^1.5.0", "pretty-format": "^27.0.2"}, "deprecated": false, "description": "Simple and complete DOM testing utilities that encourage good testing practices.", "devDependencies": {"@testing-library/jest-dom": "^5.11.6", "browserslist": "4.21.8", "caniuse-lite": "1.0.30001502", "jest-in-case": "^1.0.2", "jest-snapshot-serializer-ansi": "^1.0.0", "jest-watch-select-projects": "^2.0.0", "jsdom": "20.0.0", "kcd-scripts": "^13.0.0", "typescript": "^4.1.2"}, "engines": {"node": ">=18"}, "eslintConfig": {"extends": ["./node_modules/kcd-scripts/eslint.js", "plugin:import/typescript"], "parserOptions": {"ecmaVersion": 2020}, "rules": {"@typescript-eslint/prefer-optional-chain": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unsafe-member-access": "off", "@typescript-eslint/no-unnecessary-boolean-literal-compare": "off", "@typescript-eslint/prefer-includes": "off", "import/prefer-default-export": "off", "import/no-unassigned-import": "off", "import/no-useless-path-segments": "off", "no-console": "off"}}, "eslintIgnore": ["node_modules", "coverage", "dist"], "files": ["dist", "types/*.d.ts"], "homepage": "https://github.com/testing-library/dom-testing-library#readme", "keywords": ["testing", "ui", "dom", "jsdom", "unit", "integration", "functional", "end-to-end", "e2e"], "license": "MIT", "main": "dist/index.js", "module": "dist/@testing-library/dom.esm.js", "name": "@testing-library/dom", "overrides": {"browserslist": "4.21.8", "caniuse-lite": "1.0.30001502"}, "repository": {"type": "git", "url": "git+https://github.com/testing-library/dom-testing-library.git"}, "scripts": {"build": "kcd-scripts build  --no-ts-defs --ignore \"**/__tests__/**,**/__node_tests__/**,**/__mocks__/**\" && kcd-scripts build --no-ts-defs --bundle --no-clean", "format": "kcd-scripts format", "install:csb": "npm install", "lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:debug": "node --inspect-brk ./node_modules/.bin/jest --watch --runInBand", "test:update": "npm test -- --updateSnapshot --coverage", "typecheck": "kcd-scripts typecheck --build types", "validate": "kcd-scripts validate"}, "source": "src/index.js", "types": "types/index.d.ts", "umd:main": "dist/@testing-library/dom.umd.js", "version": "10.4.0"}