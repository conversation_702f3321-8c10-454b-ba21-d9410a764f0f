{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Board\\\\Leaderboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Leaderboard = () => {\n  _s();\n  const navigate = useNavigate();\n  // State for leaderboard data\n  const [leaderboardData, setLeaderboardData] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Fetch leaderboard data\n  useEffect(() => {\n    const fetchLeaderboard = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('http://localhost:5000/api/leaderboard');\n        if (!response.ok) {\n          throw new Error(`HTTP error! Status: ${response.status}`);\n        }\n        const data = await response.json();\n        setLeaderboardData(data);\n      } catch (err) {\n        console.error('Error fetching leaderboard:', err);\n        setError('Failed to load leaderboard data');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchLeaderboard();\n\n    // Set up polling to refresh leaderboard data\n    const intervalId = setInterval(fetchLeaderboard, 30000); // Refresh every 30 seconds\n\n    return () => clearInterval(intervalId); // Clean up on unmount\n  }, []);\n\n  // Sort data by score in descending order\n  const sortedData = [...leaderboardData].sort((a, b) => b.score - a.score);\n\n  // Find the maximum score for scaling\n  const maxScore = Math.max(...sortedData.map(user => user.score || 0), 1); // Ensure we don't divide by zero\n\n  // Handle clicking on a user's score bar to view their board\n  const handleViewUserBoard = displayName => {\n    navigate(`/boards/${encodeURIComponent(displayName)}`);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"leaderboard-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Leaderboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-message\",\n        children: \"Loading leaderboard...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"leaderboard-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Leaderboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"leaderboard-section\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Leaderboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"leaderboard-container\",\n      children: sortedData.length > 0 ? sortedData.map((user, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"leaderboard-entry\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-rank\",\n          children: index + 1\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-name\",\n          children: user.display_name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"score-bar-container\",\n          onClick: () => handleViewUserBoard(user.display_name),\n          title: `View ${user.display_name}'s board`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"score-bar\",\n            style: {\n              width: `${user.score / maxScore * 100}%`\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"score-value\",\n            children: user.score\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 15\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"no-data-message\",\n        children: \"No scores yet. Be the first to score!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 5\n  }, this);\n};\n_s(Leaderboard, \"xbJUhNYmvDR1MKI14SXkaIVjZuw=\", false, function () {\n  return [useNavigate];\n});\n_c = Leaderboard;\nexport default Leaderboard;\nvar _c;\n$RefreshReg$(_c, \"Leaderboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "jsxDEV", "_jsxDEV", "Leaderboard", "_s", "navigate", "leaderboardData", "setLeaderboardData", "loading", "setLoading", "error", "setError", "fetchLeaderboard", "response", "fetch", "ok", "Error", "status", "data", "json", "err", "console", "intervalId", "setInterval", "clearInterval", "sortedData", "sort", "a", "b", "score", "maxScore", "Math", "max", "map", "user", "handleViewUserBoard", "displayName", "encodeURIComponent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "index", "display_name", "onClick", "title", "style", "width", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/Leaderboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst Leaderboard = () => {\r\n  const navigate = useNavigate();\r\n  // State for leaderboard data\r\n  const [leaderboardData, setLeaderboardData] = useState([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  // Fetch leaderboard data\r\n  useEffect(() => {\r\n    const fetchLeaderboard = async () => {\r\n      try {\r\n        setLoading(true);\r\n\r\n        const response = await fetch('http://localhost:5000/api/leaderboard');\r\n\r\n        if (!response.ok) {\r\n          throw new Error(`HTTP error! Status: ${response.status}`);\r\n        }\r\n\r\n        const data = await response.json();\r\n        setLeaderboardData(data);\r\n      } catch (err) {\r\n        console.error('Error fetching leaderboard:', err);\r\n        setError('Failed to load leaderboard data');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchLeaderboard();\r\n\r\n    // Set up polling to refresh leaderboard data\r\n    const intervalId = setInterval(fetchLeaderboard, 30000); // Refresh every 30 seconds\r\n\r\n    return () => clearInterval(intervalId); // Clean up on unmount\r\n  }, []);\r\n\r\n  // Sort data by score in descending order\r\n  const sortedData = [...leaderboardData].sort((a, b) => b.score - a.score);\r\n\r\n  // Find the maximum score for scaling\r\n  const maxScore = Math.max(...sortedData.map(user => user.score || 0), 1); // Ensure we don't divide by zero\r\n\r\n  // Handle clicking on a user's score bar to view their board\r\n  const handleViewUserBoard = (displayName) => {\r\n    navigate(`/boards/${encodeURIComponent(displayName)}`);\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"leaderboard-section\">\r\n        <h2>Leaderboard</h2>\r\n        <div className=\"loading-message\">Loading leaderboard...</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"leaderboard-section\">\r\n        <h2>Leaderboard</h2>\r\n        <div className=\"error-message\">{error}</div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"leaderboard-section\">\r\n      <h2>Leaderboard</h2>\r\n      <div className=\"leaderboard-container\">\r\n        {sortedData.length > 0 ? (\r\n          sortedData.map((user, index) => (\r\n            <div key={index} className=\"leaderboard-entry\">\r\n              <div className=\"user-rank\">{index + 1}</div>\r\n              <div className=\"user-name\">{user.display_name}</div>\r\n              <div\r\n                className=\"score-bar-container\"\r\n                onClick={() => handleViewUserBoard(user.display_name)}\r\n                title={`View ${user.display_name}'s board`}\r\n              >\r\n                <div\r\n                  className=\"score-bar\"\r\n                  style={{ width: `${(user.score / maxScore) * 100}%` }}\r\n                ></div>\r\n                <span className=\"score-value\">{user.score}</span>\r\n              </div>\r\n            </div>\r\n          ))\r\n        ) : (\r\n          <div className=\"no-data-message\">No scores yet. Be the first to score!</div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Leaderboard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAC9B;EACA,MAAM,CAACM,eAAe,EAAEC,kBAAkB,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMa,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACFH,UAAU,CAAC,IAAI,CAAC;QAEhB,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,uCAAuC,CAAC;QAErE,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCZ,kBAAkB,CAACW,IAAI,CAAC;MAC1B,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACX,KAAK,CAAC,6BAA6B,EAAEU,GAAG,CAAC;QACjDT,QAAQ,CAAC,iCAAiC,CAAC;MAC7C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,gBAAgB,CAAC,CAAC;;IAElB;IACA,MAAMU,UAAU,GAAGC,WAAW,CAACX,gBAAgB,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEzD,OAAO,MAAMY,aAAa,CAACF,UAAU,CAAC,CAAC,CAAC;EAC1C,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMG,UAAU,GAAG,CAAC,GAAGnB,eAAe,CAAC,CAACoB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACC,KAAK,GAAGF,CAAC,CAACE,KAAK,CAAC;;EAEzE;EACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGP,UAAU,CAACQ,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACL,KAAK,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAE1E;EACA,MAAMM,mBAAmB,GAAIC,WAAW,IAAK;IAC3C/B,QAAQ,CAAC,WAAWgC,kBAAkB,CAACD,WAAW,CAAC,EAAE,CAAC;EACxD,CAAC;EAED,IAAI5B,OAAO,EAAE;IACX,oBACEN,OAAA;MAAKoC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCrC,OAAA;QAAAqC,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBzC,OAAA;QAAKoC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1D,CAAC;EAEV;EAEA,IAAIjC,KAAK,EAAE;IACT,oBACER,OAAA;MAAKoC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCrC,OAAA;QAAAqC,QAAA,EAAI;MAAW;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBzC,OAAA;QAAKoC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAE7B;MAAK;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAEV;EAEA,oBACEzC,OAAA;IAAKoC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCrC,OAAA;MAAAqC,QAAA,EAAI;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpBzC,OAAA;MAAKoC,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EACnCd,UAAU,CAACmB,MAAM,GAAG,CAAC,GACpBnB,UAAU,CAACQ,GAAG,CAAC,CAACC,IAAI,EAAEW,KAAK,kBACzB3C,OAAA;QAAiBoC,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAC5CrC,OAAA;UAAKoC,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEM,KAAK,GAAG;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5CzC,OAAA;UAAKoC,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEL,IAAI,CAACY;QAAY;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpDzC,OAAA;UACEoC,SAAS,EAAC,qBAAqB;UAC/BS,OAAO,EAAEA,CAAA,KAAMZ,mBAAmB,CAACD,IAAI,CAACY,YAAY,CAAE;UACtDE,KAAK,EAAE,QAAQd,IAAI,CAACY,YAAY,UAAW;UAAAP,QAAA,gBAE3CrC,OAAA;YACEoC,SAAS,EAAC,WAAW;YACrBW,KAAK,EAAE;cAAEC,KAAK,EAAE,GAAIhB,IAAI,CAACL,KAAK,GAAGC,QAAQ,GAAI,GAAG;YAAI;UAAE;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACPzC,OAAA;YAAMoC,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEL,IAAI,CAACL;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA,GAbEE,KAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcV,CACN,CAAC,gBAEFzC,OAAA;QAAKoC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAqC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAC5E;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CA9FID,WAAW;EAAA,QACEH,WAAW;AAAA;AAAAmD,EAAA,GADxBhD,WAAW;AAgGjB,eAAeA,WAAW;AAAC,IAAAgD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}