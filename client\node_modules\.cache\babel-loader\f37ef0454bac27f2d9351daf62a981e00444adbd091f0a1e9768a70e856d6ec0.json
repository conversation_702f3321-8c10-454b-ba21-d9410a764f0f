{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Board\\\\ConfirmationModal.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ConfirmationModal = ({\n  onCancel,\n  onConfirm\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"confirmation-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"confirmation-dialog\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Warning!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Refreshing your board will delete your previous one and restart your progress. Are you sure you want to continue?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"confirmation-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"cancel-button\",\n          onClick: onCancel,\n          children: \"Cancel\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"confirm-button\",\n          onClick: onConfirm,\n          children: \"Yes, Refresh My Board\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = ConfirmationModal;\nexport default ConfirmationModal;\nvar _c;\n$RefreshReg$(_c, \"ConfirmationModal\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "ConfirmationModal", "onCancel", "onConfirm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/ConfirmationModal.js"], "sourcesContent": ["import React from 'react';\r\n\r\nconst ConfirmationModal = ({ onCancel, onConfirm }) => {\r\n  return (\r\n    <div className=\"confirmation-overlay\">\r\n      <div className=\"confirmation-dialog\">\r\n        <h3>Warning!</h3>\r\n        <p>\r\n          Refreshing your board will delete your previous one and restart your progress.\r\n          Are you sure you want to continue?\r\n        </p>\r\n        <div className=\"confirmation-buttons\">\r\n          <button className=\"cancel-button\" onClick={onCancel}>\r\n            Cancel\r\n          </button>\r\n          <button className=\"confirm-button\" onClick={onConfirm}>\r\n            Yes, Refresh My Board\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ConfirmationModal;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAU,CAAC,KAAK;EACrD,oBACEH,OAAA;IAAKI,SAAS,EAAC,sBAAsB;IAAAC,QAAA,eACnCL,OAAA;MAAKI,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCL,OAAA;QAAAK,QAAA,EAAI;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACjBT,OAAA;QAAAK,QAAA,EAAG;MAGH;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJT,OAAA;QAAKI,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCL,OAAA;UAAQI,SAAS,EAAC,eAAe;UAACM,OAAO,EAAER,QAAS;UAAAG,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTT,OAAA;UAAQI,SAAS,EAAC,gBAAgB;UAACM,OAAO,EAAEP,SAAU;UAAAE,QAAA,EAAC;QAEvD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GApBIV,iBAAiB;AAsBvB,eAAeA,iBAAiB;AAAC,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}