{"_from": "yocto-queue@^0.1.0", "_id": "yocto-queue@0.1.0", "_inBundle": false, "_integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==", "_location": "/yocto-queue", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "yocto-queue@^0.1.0", "name": "yocto-queue", "escapedName": "yocto-queue", "rawSpec": "^0.1.0", "saveSpec": null, "fetchSpec": "^0.1.0"}, "_requiredBy": ["/eslint/p-limit", "/react-dev-utils/p-limit"], "_resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz", "_shasum": "0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b", "_spec": "yocto-queue@^0.1.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\node_modules\\eslint\\node_modules\\p-limit", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/yocto-queue/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Tiny queue data structure", "devDependencies": {"ava": "^2.4.0", "tsd": "^0.13.1", "xo": "^0.35.0"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/yocto-queue#readme", "keywords": ["queue", "data", "structure", "algorithm", "queues", "queuing", "list", "array", "linkedlist", "fifo", "enqueue", "dequeue", "data-structure"], "license": "MIT", "name": "yocto-queue", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/yocto-queue.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "0.1.0"}