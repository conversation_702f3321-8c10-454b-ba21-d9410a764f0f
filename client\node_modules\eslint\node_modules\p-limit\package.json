{"_from": "p-limit@^3.0.2", "_id": "p-limit@3.1.0", "_inBundle": false, "_integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==", "_location": "/eslint/p-limit", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "p-limit@^3.0.2", "name": "p-limit", "escapedName": "p-limit", "rawSpec": "^3.0.2", "saveSpec": null, "fetchSpec": "^3.0.2"}, "_requiredBy": ["/eslint/p-locate"], "_resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "_shasum": "e1daccbe78d0d1388ca18c64fea38e3e57e3706b", "_spec": "p-limit@^3.0.2", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\node_modules\\eslint\\node_modules\\p-locate", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-limit/issues"}, "bundleDependencies": false, "dependencies": {"yocto-queue": "^0.1.0"}, "deprecated": false, "description": "Run multiple promise-returning & async functions with limited concurrency", "devDependencies": {"ava": "^2.4.0", "delay": "^4.4.0", "in-range": "^2.0.0", "random-int": "^2.0.1", "time-span": "^4.0.0", "tsd": "^0.13.1", "xo": "^0.35.0"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/p-limit#readme", "keywords": ["promise", "limit", "limited", "concurrency", "throttle", "throat", "rate", "batch", "ratelimit", "task", "queue", "async", "await", "promises", "bluebird"], "license": "MIT", "name": "p-limit", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-limit.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "3.1.0"}