{"_from": "jest-cli@^27.5.1", "_id": "jest-cli@27.5.1", "_inBundle": false, "_integrity": "sha512-Hc6HOOwYq4/74/c62dEE3r5elx8wjYqxY0r0G/nFrLDPMFRu6RA/u8qINOIkvhxG7mMQ5EJsOGfRpI8L6eFUVw==", "_location": "/jest/jest-cli", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "jest-cli@^27.5.1", "name": "jest-cli", "escapedName": "jest-cli", "rawSpec": "^27.5.1", "saveSpec": null, "fetchSpec": "^27.5.1"}, "_requiredBy": ["/jest"], "_resolved": "https://registry.npmjs.org/jest-cli/-/jest-cli-27.5.1.tgz", "_shasum": "278794a6e6458ea8029547e6c6cbf673bd30b145", "_spec": "jest-cli@^27.5.1", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\node_modules\\jest", "bin": {"jest": "bin/jest.js"}, "bugs": {"url": "https://github.com/facebook/jest/issues"}, "bundleDependencies": false, "dependencies": {"@jest/core": "^27.5.1", "@jest/test-result": "^27.5.1", "@jest/types": "^27.5.1", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "import-local": "^3.0.2", "jest-config": "^27.5.1", "jest-util": "^27.5.1", "jest-validate": "^27.5.1", "prompts": "^2.0.1", "yargs": "^16.2.0"}, "deprecated": false, "description": "Delightful JavaScript Testing.", "devDependencies": {"@types/exit": "^0.1.30", "@types/graceful-fs": "^4.1.3", "@types/prompts": "^2.0.1", "@types/yargs": "^16.0.0"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json", "./bin/jest": "./bin/jest.js"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "homepage": "https://jestjs.io/", "keywords": ["ava", "babel", "coverage", "easy", "expect", "facebook", "immersive", "instant", "jasmine", "jest", "jsdom", "mocha", "mocking", "painless", "qunit", "runner", "sandboxed", "snapshot", "tap", "tape", "test", "testing", "typescript", "watch"], "license": "MIT", "main": "./build/index.js", "name": "jest-cli", "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/facebook/jest.git", "directory": "packages/jest-cli"}, "types": "./build/index.d.ts", "version": "27.5.1"}