{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Cards\\\\Cards.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport PortraitOverlay from '../Board/PortraitOverlay';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Cards = () => {\n  _s();\n  const [characters, setCharacters] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedCharacter, setSelectedCharacter] = useState(null);\n\n  // Fetch all characters\n  useEffect(() => {\n    const fetchCharacters = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('http://localhost:5000/api/characters');\n        if (!response.ok) {\n          throw new Error(`HTTP error! Status: ${response.status}`);\n        }\n        const data = await response.json();\n        setCharacters(data);\n      } catch (err) {\n        console.error('Error fetching characters:', err);\n        setError('Failed to load characters');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchCharacters();\n  }, []);\n  const handleCharacterClick = (character, index) => {\n    setSelectedCharacter(character);\n  };\n  const handleCloseOverlay = () => {\n    setSelectedCharacter(null);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cards-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"All Available Cards\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-message\",\n        children: \"Loading cards...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cards-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"All Available Cards\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Group characters by rarity\n  const groupedCharacters = characters.reduce((groups, character) => {\n    const rarity = character.rarity || 'R';\n    if (!groups[rarity]) {\n      groups[rarity] = [];\n    }\n    groups[rarity].push(character);\n    return groups;\n  }, {});\n\n  // Define rarity order and colors\n  const rarityOrder = ['FREE', 'R', 'SR', 'SSR', 'UR+'];\n  const rarityColors = {\n    'FREE': '#4fc3f7',\n    'R': '#81c784',\n    'SR': '#ffb74d',\n    'SSR': '#e57373',\n    'UR+': '#ba68c8'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cards-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"All Available Cards\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"cards-content\",\n      children: rarityOrder.map(rarity => {\n        const rarityCharacters = groupedCharacters[rarity] || [];\n        if (rarityCharacters.length === 0) return null;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"rarity-section\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"rarity-title\",\n            style: {\n              color: rarityColors[rarity]\n            },\n            children: [rarity, \" (\", rarityCharacters.length, \" cards)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 90,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"cards-grid\",\n            children: rarityCharacters.map((character, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"card-item\",\n              onClick: () => handleCharacterClick(character, index),\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-image-container\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `${process.env.PUBLIC_URL}${character.Thumbnail}`,\n                  alt: character.Name,\n                  className: \"card-thumbnail\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 101,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: `${process.env.PUBLIC_URL}/frames/${character.rarity}.png`,\n                  alt: `${character.rarity} frame`,\n                  className: \"card-frame\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card-info\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"card-name\",\n                  children: character.Name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 113,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"card-source\",\n                  children: character.Source\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 112,\n                columnNumber: 21\n              }, this)]\n            }, `${rarity}-${index}`, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 93,\n            columnNumber: 15\n          }, this)]\n        }, rarity, true, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), selectedCharacter && /*#__PURE__*/_jsxDEV(PortraitOverlay, {\n      character: selectedCharacter,\n      onClose: handleCloseOverlay,\n      onClaim: () => {} // No claim functionality in cards view\n      ,\n      isClaimed: false,\n      isReadOnly: true // Make it read-only so no claim button shows\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(Cards, \"bqAuZCzo1zk//ooj86VwnZ0ovgI=\");\n_c = Cards;\nexport default Cards;\nvar _c;\n$RefreshReg$(_c, \"Cards\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "PortraitOverlay", "jsxDEV", "_jsxDEV", "Cards", "_s", "characters", "setChara<PERSON><PERSON>", "loading", "setLoading", "error", "setError", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedCharacter", "fetchCharacters", "response", "fetch", "ok", "Error", "status", "data", "json", "err", "console", "handleCharacterClick", "character", "index", "handleCloseOverlay", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "groupedCharacters", "reduce", "groups", "rarity", "push", "rarityOrder", "rarityColors", "map", "rarityCharacters", "length", "style", "color", "onClick", "src", "process", "env", "PUBLIC_URL", "<PERSON><PERSON><PERSON><PERSON>", "alt", "Name", "Source", "onClose", "onClaim", "isClaimed", "isReadOnly", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Cards/Cards.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport PortraitOverlay from '../Board/PortraitOverlay';\n\nconst Cards = () => {\n  const [characters, setCharacters] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [selectedCharacter, setSelectedCharacter] = useState(null);\n\n  // Fetch all characters\n  useEffect(() => {\n    const fetchCharacters = async () => {\n      try {\n        setLoading(true);\n        const response = await fetch('http://localhost:5000/api/characters');\n        \n        if (!response.ok) {\n          throw new Error(`HTTP error! Status: ${response.status}`);\n        }\n        \n        const data = await response.json();\n        setCharacters(data);\n      } catch (err) {\n        console.error('Error fetching characters:', err);\n        setError('Failed to load characters');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchCharacters();\n  }, []);\n\n  const handleCharacterClick = (character, index) => {\n    setSelectedCharacter(character);\n  };\n\n  const handleCloseOverlay = () => {\n    setSelectedCharacter(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"cards-container\">\n        <h1>All Available Cards</h1>\n        <div className=\"loading-message\">Loading cards...</div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"cards-container\">\n        <h1>All Available Cards</h1>\n        <div className=\"error-message\">{error}</div>\n      </div>\n    );\n  }\n\n  // Group characters by rarity\n  const groupedCharacters = characters.reduce((groups, character) => {\n    const rarity = character.rarity || 'R';\n    if (!groups[rarity]) {\n      groups[rarity] = [];\n    }\n    groups[rarity].push(character);\n    return groups;\n  }, {});\n\n  // Define rarity order and colors\n  const rarityOrder = ['FREE', 'R', 'SR', 'SSR', 'UR+'];\n  const rarityColors = {\n    'FREE': '#4fc3f7',\n    'R': '#81c784',\n    'SR': '#ffb74d',\n    'SSR': '#e57373',\n    'UR+': '#ba68c8'\n  };\n\n  return (\n    <div className=\"cards-container\">\n      <h1>All Available Cards</h1>\n      <div className=\"cards-content\">\n        {rarityOrder.map(rarity => {\n          const rarityCharacters = groupedCharacters[rarity] || [];\n          if (rarityCharacters.length === 0) return null;\n\n          return (\n            <div key={rarity} className=\"rarity-section\">\n              <h2 className=\"rarity-title\" style={{ color: rarityColors[rarity] }}>\n                {rarity} ({rarityCharacters.length} cards)\n              </h2>\n              <div className=\"cards-grid\">\n                {rarityCharacters.map((character, index) => (\n                  <div\n                    key={`${rarity}-${index}`}\n                    className=\"card-item\"\n                    onClick={() => handleCharacterClick(character, index)}\n                  >\n                    <div className=\"card-image-container\">\n                      <img\n                        src={`${process.env.PUBLIC_URL}${character.Thumbnail}`}\n                        alt={character.Name}\n                        className=\"card-thumbnail\"\n                      />\n                      <img\n                        src={`${process.env.PUBLIC_URL}/frames/${character.rarity}.png`}\n                        alt={`${character.rarity} frame`}\n                        className=\"card-frame\"\n                      />\n                    </div>\n                    <div className=\"card-info\">\n                      <h3 className=\"card-name\">{character.Name}</h3>\n                      <p className=\"card-source\">{character.Source}</p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {selectedCharacter && (\n        <PortraitOverlay\n          character={selectedCharacter}\n          onClose={handleCloseOverlay}\n          onClaim={() => {}} // No claim functionality in cards view\n          isClaimed={false}\n          isReadOnly={true} // Make it read-only so no claim button shows\n        />\n      )}\n    </div>\n  );\n};\n\nexport default Cards;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,eAAe,MAAM,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACa,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;;EAEhE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMc,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACFL,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC,CAAC;QAEpE,IAAI,CAACD,QAAQ,CAACE,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,QAAQ,CAACI,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;QAClCd,aAAa,CAACa,IAAI,CAAC;MACrB,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZC,OAAO,CAACb,KAAK,CAAC,4BAA4B,EAAEY,GAAG,CAAC;QAChDX,QAAQ,CAAC,2BAA2B,CAAC;MACvC,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDK,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,oBAAoB,GAAGA,CAACC,SAAS,EAAEC,KAAK,KAAK;IACjDb,oBAAoB,CAACY,SAAS,CAAC;EACjC,CAAC;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/Bd,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,IAAIL,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKyB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1B,OAAA;QAAA0B,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5B9B,OAAA;QAAKyB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC;EAEV;EAEA,IAAIvB,KAAK,EAAE;IACT,oBACEP,OAAA;MAAKyB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9B1B,OAAA;QAAA0B,QAAA,EAAI;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5B9B,OAAA;QAAKyB,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAEnB;MAAK;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzC,CAAC;EAEV;;EAEA;EACA,MAAMC,iBAAiB,GAAG5B,UAAU,CAAC6B,MAAM,CAAC,CAACC,MAAM,EAAEX,SAAS,KAAK;IACjE,MAAMY,MAAM,GAAGZ,SAAS,CAACY,MAAM,IAAI,GAAG;IACtC,IAAI,CAACD,MAAM,CAACC,MAAM,CAAC,EAAE;MACnBD,MAAM,CAACC,MAAM,CAAC,GAAG,EAAE;IACrB;IACAD,MAAM,CAACC,MAAM,CAAC,CAACC,IAAI,CAACb,SAAS,CAAC;IAC9B,OAAOW,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC;;EAEN;EACA,MAAMG,WAAW,GAAG,CAAC,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC;EACrD,MAAMC,YAAY,GAAG;IACnB,MAAM,EAAE,SAAS;IACjB,GAAG,EAAE,SAAS;IACd,IAAI,EAAE,SAAS;IACf,KAAK,EAAE,SAAS;IAChB,KAAK,EAAE;EACT,CAAC;EAED,oBACErC,OAAA;IAAKyB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,gBAC9B1B,OAAA;MAAA0B,QAAA,EAAI;IAAmB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC5B9B,OAAA;MAAKyB,SAAS,EAAC,eAAe;MAAAC,QAAA,EAC3BU,WAAW,CAACE,GAAG,CAACJ,MAAM,IAAI;QACzB,MAAMK,gBAAgB,GAAGR,iBAAiB,CAACG,MAAM,CAAC,IAAI,EAAE;QACxD,IAAIK,gBAAgB,CAACC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;QAE9C,oBACExC,OAAA;UAAkByB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,gBAC1C1B,OAAA;YAAIyB,SAAS,EAAC,cAAc;YAACgB,KAAK,EAAE;cAAEC,KAAK,EAAEL,YAAY,CAACH,MAAM;YAAE,CAAE;YAAAR,QAAA,GACjEQ,MAAM,EAAC,IAAE,EAACK,gBAAgB,CAACC,MAAM,EAAC,SACrC;UAAA;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACL9B,OAAA;YAAKyB,SAAS,EAAC,YAAY;YAAAC,QAAA,EACxBa,gBAAgB,CAACD,GAAG,CAAC,CAAChB,SAAS,EAAEC,KAAK,kBACrCvB,OAAA;cAEEyB,SAAS,EAAC,WAAW;cACrBkB,OAAO,EAAEA,CAAA,KAAMtB,oBAAoB,CAACC,SAAS,EAAEC,KAAK,CAAE;cAAAG,QAAA,gBAEtD1B,OAAA;gBAAKyB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,gBACnC1B,OAAA;kBACE4C,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,GAAGzB,SAAS,CAAC0B,SAAS,EAAG;kBACvDC,GAAG,EAAE3B,SAAS,CAAC4B,IAAK;kBACpBzB,SAAS,EAAC;gBAAgB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B,CAAC,eACF9B,OAAA;kBACE4C,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,WAAWzB,SAAS,CAACY,MAAM,MAAO;kBAChEe,GAAG,EAAE,GAAG3B,SAAS,CAACY,MAAM,QAAS;kBACjCT,SAAS,EAAC;gBAAY;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN9B,OAAA;gBAAKyB,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxB1B,OAAA;kBAAIyB,SAAS,EAAC,WAAW;kBAAAC,QAAA,EAAEJ,SAAS,CAAC4B;gBAAI;kBAAAvB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC/C9B,OAAA;kBAAGyB,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEJ,SAAS,CAAC6B;gBAAM;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC;YAAA,GAnBD,GAAGI,MAAM,IAAIX,KAAK,EAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBtB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA,GA7BEI,MAAM;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BX,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAELrB,iBAAiB,iBAChBT,OAAA,CAACF,eAAe;MACdwB,SAAS,EAAEb,iBAAkB;MAC7B2C,OAAO,EAAE5B,kBAAmB;MAC5B6B,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAE,CAAC;MAAA;MACnBC,SAAS,EAAE,KAAM;MACjBC,UAAU,EAAE,IAAK,CAAC;IAAA;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC5B,EAAA,CAnIID,KAAK;AAAAuD,EAAA,GAALvD,KAAK;AAqIX,eAAeA,KAAK;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}