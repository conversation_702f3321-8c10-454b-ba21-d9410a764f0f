{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Dashboard\\\\UserDashboard.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useUser } from '../Auth/UserContext';\nimport { useNavigate } from 'react-router-dom';\nimport { Rules } from '../../App';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserDashboard = () => {\n  _s();\n  // Get user context and navigation\n  const {\n    user,\n    logout\n  } = useUser();\n  const navigate = useNavigate();\n\n  // State for users list and user's board\n  const [users, setUsers] = useState([]);\n  const [userBoard, setUserBoard] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showRefreshConfirmation, setShowRefreshConfirmation] = useState(false);\n  const [showAgreementModal, setShowAgreementModal] = useState(false);\n  const [hasAgreedToTerms, setHasAgreedToTerms] = useState(false);\n\n  // Fetch all users and check if current user has a board on component mount\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        // Fetch all users\n        const usersResponse = await fetch('http://localhost:5000/api/users');\n        if (!usersResponse.ok) {\n          throw new Error(`HTTP error! Status: ${usersResponse.status}`);\n        }\n        const usersData = await usersResponse.json();\n        setUsers(usersData);\n\n        // Check if user has a board\n        if (user) {\n          const boardResponse = await fetch(`http://localhost:5000/api/users/${user.id}/board`, {\n            method: 'GET',\n            headers: {\n              'Content-Type': 'application/json'\n            }\n          });\n          if (boardResponse.ok) {\n            const boardData = await boardResponse.json();\n            setUserBoard(boardData);\n            // If user has a board, redirect them to their board immediately\n            navigate(`/boards/${encodeURIComponent(user.display_name)}`);\n            return; // Exit early since we're redirecting\n          } else if (boardResponse.status !== 404) {\n            // Only throw error if it's not a 404 (no board found)\n            throw new Error(`HTTP error! Status: ${boardResponse.status}`);\n          }\n        }\n      } catch (err) {\n        console.error('Error fetching data:', err);\n        setError('Failed to load data. Please try again later.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchData();\n  }, [user]);\n\n  // Handle logout\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n\n  // Navigate to current user's board\n  const viewMyBoard = () => {\n    navigate(`/boards/${encodeURIComponent(user.display_name)}`);\n  };\n\n  // Navigate to another user's board\n  const viewUserBoard = displayName => {\n    navigate(`/boards/${encodeURIComponent(displayName)}`);\n  };\n\n  // Show the agreement modal\n  const handleStartPlayingClick = () => {\n    setShowAgreementModal(true);\n  };\n\n  // Handle agreement modal confirmation\n  const handleAgreementConfirm = () => {\n    if (!hasAgreedToTerms) {\n      setError('Please agree to review the How to Play section and Rules before starting.');\n      return;\n    }\n    setShowAgreementModal(false);\n    createNewBoard();\n  };\n\n  // Handle agreement modal cancellation\n  const handleAgreementCancel = () => {\n    setShowAgreementModal(false);\n    setHasAgreedToTerms(false);\n  };\n\n  // Create a new board for the current user\n  const createNewBoard = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await fetch(`http://localhost:5000/api/users/${user.id}/board`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! Status: ${response.status}`);\n      }\n\n      // Update local state with the new board\n      const boardData = await response.json();\n      setUserBoard(boardData);\n\n      // Navigate to the new board\n      navigate(`/boards/${encodeURIComponent(user.display_name)}`);\n    } catch (err) {\n      console.error('Error creating new board:', err);\n      setError('Failed to create new board. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle refresh board button click\n  const handleRefreshClick = () => {\n    setShowRefreshConfirmation(true);\n  };\n\n  // Handle cancel refresh\n  const handleCancelRefresh = () => {\n    setShowRefreshConfirmation(false);\n  };\n\n  // Handle confirm refresh\n  const handleConfirmRefresh = async () => {\n    try {\n      setLoading(true);\n      setShowRefreshConfirmation(false);\n      const response = await fetch(`http://localhost:5000/api/users/${user.id}/board`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        }\n      });\n      if (!response.ok) {\n        throw new Error(`HTTP error! Status: ${response.status}`);\n      }\n\n      // Update local state with the new board\n      const boardData = await response.json();\n      setUserBoard(boardData);\n\n      // Navigate to the new board\n      navigate(`/boards/${encodeURIComponent(user.display_name)}`);\n    } catch (err) {\n      console.error('Error refreshing board:', err);\n      setError('Failed to refresh board. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // If user is not logged in, redirect to login page\n  if (!user) {\n    navigate('/');\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"dashboard-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: [\"Welcome, \", user.display_name, \"!\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleLogout,\n        className: \"logout-button\",\n        children: \"Logout\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-actions\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-message\",\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this) : !userBoard ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"board-creation-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Ready to Start Playing?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleStartPlayingClick,\n          className: \"start-playing-button enabled\",\n          disabled: loading,\n          children: \"Start Playing!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this) : null\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-message\",\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 204,\n      columnNumber: 17\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"users-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Other Players\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-message\",\n        children: \"Loading users...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"users-list\",\n        children: [users.filter(u => u.id !== user.id) // Filter out current user\n        .map(u => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-name\",\n            children: u.display_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => viewUserBoard(u.display_name),\n            className: \"view-board-button\",\n            children: \"View Board\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 19\n          }, this)]\n        }, u.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 17\n        }, this)), users.length <= 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"no-users-message\",\n          children: \"No other players yet. Invite your friends!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 206,\n      columnNumber: 7\n    }, this), showRefreshConfirmation && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"confirmation-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"confirmation-dialog\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Warning!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Refreshing your board will delete your previous one and restart your progress. Are you sure you want to continue?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleCancelRefresh,\n            className: \"cancel-button\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleConfirmRefresh,\n            className: \"confirm-button\",\n            children: \"Yes, Refresh My Board\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 243,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 9\n    }, this), showAgreementModal && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"confirmation-overlay\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"agreement-modal\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Before You Start Playing\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"agreement-text\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"Before playing or making any complaints, you must read the \", /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"/how-to-play\",\n              target: \"_blank\",\n              rel: \"noopener noreferrer\",\n              children: \"How to Play\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 76\n            }, this), \" section and the \", /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#rules\",\n              children: \"Rules\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 173\n            }, this), \" to understand the game mechanics, scoring system, and proper etiquette.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"agreement-checkbox-section\",\n          children: /*#__PURE__*/_jsxDEV(\"label\", {\n            className: \"agreement-checkbox\",\n            children: [/*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"checkbox\",\n              checked: hasAgreedToTerms,\n              onChange: e => setHasAgreedToTerms(e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this), \"I promise\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"confirmation-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAgreementCancel,\n            className: \"cancel-button\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleAgreementConfirm,\n            className: `confirm-button ${hasAgreedToTerms ? 'enabled' : 'disabled'}`,\n            disabled: !hasAgreedToTerms,\n            children: \"Start Playing!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-info-sections\",\n      children: /*#__PURE__*/_jsxDEV(Rules, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 293,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 292,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(UserDashboard, \"jTMd1JWo0iej3Hr34K/CDDZfV3k=\", false, function () {\n  return [useUser, useNavigate];\n});\n_c = UserDashboard;\nexport default UserDashboard;\nvar _c;\n$RefreshReg$(_c, \"UserDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useUser", "useNavigate", "Rules", "jsxDEV", "_jsxDEV", "UserDashboard", "_s", "user", "logout", "navigate", "users", "setUsers", "userBoard", "setUserBoard", "loading", "setLoading", "error", "setError", "showRefreshConfirmation", "setShowRefreshConfirmation", "showAgreementModal", "setShowAgreementModal", "hasAgreedToTerms", "setHasAgreedToTerms", "fetchData", "usersResponse", "fetch", "ok", "Error", "status", "usersData", "json", "boardResponse", "id", "method", "headers", "boardData", "encodeURIComponent", "display_name", "err", "console", "handleLogout", "viewMyBoard", "viewUserBoard", "displayName", "handleStartPlayingClick", "handleAgreementConfirm", "createNewBoard", "handleAgreementCancel", "response", "handleRefreshClick", "handleCancelRefresh", "handleConfirmRefresh", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "filter", "u", "map", "length", "href", "target", "rel", "type", "checked", "onChange", "e", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Dashboard/UserDashboard.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useUser } from '../Auth/UserContext';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { Rules } from '../../App';\r\n\r\nconst UserDashboard = () => {\r\n  // Get user context and navigation\r\n  const { user, logout } = useUser();\r\n  const navigate = useNavigate();\r\n\r\n  // State for users list and user's board\r\n  const [users, setUsers] = useState([]);\r\n  const [userBoard, setUserBoard] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [showRefreshConfirmation, setShowRefreshConfirmation] = useState(false);\r\n  const [showAgreementModal, setShowAgreementModal] = useState(false);\r\n  const [hasAgreedToTerms, setHasAgreedToTerms] = useState(false);\r\n\r\n  // Fetch all users and check if current user has a board on component mount\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        // Fetch all users\r\n        const usersResponse = await fetch('http://localhost:5000/api/users');\r\n\r\n        if (!usersResponse.ok) {\r\n          throw new Error(`HTTP error! Status: ${usersResponse.status}`);\r\n        }\r\n\r\n        const usersData = await usersResponse.json();\r\n        setUsers(usersData);\r\n\r\n        // Check if user has a board\r\n        if (user) {\r\n          const boardResponse = await fetch(`http://localhost:5000/api/users/${user.id}/board`, {\r\n            method: 'GET',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n          });\r\n\r\n          if (boardResponse.ok) {\r\n            const boardData = await boardResponse.json();\r\n            setUserBoard(boardData);\r\n            // If user has a board, redirect them to their board immediately\r\n            navigate(`/boards/${encodeURIComponent(user.display_name)}`);\r\n            return; // Exit early since we're redirecting\r\n          } else if (boardResponse.status !== 404) {\r\n            // Only throw error if it's not a 404 (no board found)\r\n            throw new Error(`HTTP error! Status: ${boardResponse.status}`);\r\n          }\r\n        }\r\n      } catch (err) {\r\n        console.error('Error fetching data:', err);\r\n        setError('Failed to load data. Please try again later.');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchData();\r\n  }, [user]);\r\n\r\n  // Handle logout\r\n  const handleLogout = () => {\r\n    logout();\r\n    navigate('/');\r\n  };\r\n\r\n  // Navigate to current user's board\r\n  const viewMyBoard = () => {\r\n    navigate(`/boards/${encodeURIComponent(user.display_name)}`);\r\n  };\r\n\r\n  // Navigate to another user's board\r\n  const viewUserBoard = (displayName) => {\r\n    navigate(`/boards/${encodeURIComponent(displayName)}`);\r\n  };\r\n\r\n  // Show the agreement modal\r\n  const handleStartPlayingClick = () => {\r\n    setShowAgreementModal(true);\r\n  };\r\n\r\n  // Handle agreement modal confirmation\r\n  const handleAgreementConfirm = () => {\r\n    if (!hasAgreedToTerms) {\r\n      setError('Please agree to review the How to Play section and Rules before starting.');\r\n      return;\r\n    }\r\n    setShowAgreementModal(false);\r\n    createNewBoard();\r\n  };\r\n\r\n  // Handle agreement modal cancellation\r\n  const handleAgreementCancel = () => {\r\n    setShowAgreementModal(false);\r\n    setHasAgreedToTerms(false);\r\n  };\r\n\r\n  // Create a new board for the current user\r\n  const createNewBoard = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      const response = await fetch(`http://localhost:5000/api/users/${user.id}/board`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! Status: ${response.status}`);\r\n      }\r\n\r\n      // Update local state with the new board\r\n      const boardData = await response.json();\r\n      setUserBoard(boardData);\r\n\r\n      // Navigate to the new board\r\n      navigate(`/boards/${encodeURIComponent(user.display_name)}`);\r\n    } catch (err) {\r\n      console.error('Error creating new board:', err);\r\n      setError('Failed to create new board. Please try again later.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Handle refresh board button click\r\n  const handleRefreshClick = () => {\r\n    setShowRefreshConfirmation(true);\r\n  };\r\n\r\n  // Handle cancel refresh\r\n  const handleCancelRefresh = () => {\r\n    setShowRefreshConfirmation(false);\r\n  };\r\n\r\n  // Handle confirm refresh\r\n  const handleConfirmRefresh = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setShowRefreshConfirmation(false);\r\n\r\n      const response = await fetch(`http://localhost:5000/api/users/${user.id}/board`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! Status: ${response.status}`);\r\n      }\r\n\r\n      // Update local state with the new board\r\n      const boardData = await response.json();\r\n      setUserBoard(boardData);\r\n\r\n      // Navigate to the new board\r\n      navigate(`/boards/${encodeURIComponent(user.display_name)}`);\r\n    } catch (err) {\r\n      console.error('Error refreshing board:', err);\r\n      setError('Failed to refresh board. Please try again later.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // If user is not logged in, redirect to login page\r\n  if (!user) {\r\n    navigate('/');\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"dashboard-container\">\r\n      <div className=\"dashboard-header\">\r\n        <h1>Welcome, {user.display_name}!</h1>\r\n        <button onClick={handleLogout} className=\"logout-button\">Logout</button>\r\n      </div>\r\n\r\n      <div className=\"dashboard-actions\">\r\n        {loading ? (\r\n          <div className=\"loading-message\">Loading...</div>\r\n        ) : !userBoard ? (\r\n          <div className=\"board-creation-section\">\r\n            <h2>Ready to Start Playing?</h2>\r\n            <button\r\n              onClick={handleStartPlayingClick}\r\n              className=\"start-playing-button enabled\"\r\n              disabled={loading}\r\n            >\r\n              Start Playing!\r\n            </button>\r\n          </div>\r\n        ) : null}\r\n      </div>\r\n\r\n      {error && <div className=\"error-message\">{error}</div>}\r\n\r\n      <div className=\"users-section\">\r\n        <h2>Other Players</h2>\r\n        {loading ? (\r\n          <div className=\"loading-message\">Loading users...</div>\r\n        ) : (\r\n          <div className=\"users-list\">\r\n            {users\r\n              .filter(u => u.id !== user.id) // Filter out current user\r\n              .map(u => (\r\n                <div key={u.id} className=\"user-item\">\r\n                  <span className=\"user-name\">{u.display_name}</span>\r\n                  <button\r\n                    onClick={() => viewUserBoard(u.display_name)}\r\n                    className=\"view-board-button\"\r\n                  >\r\n                    View Board\r\n                  </button>\r\n                </div>\r\n              ))}\r\n            {users.length <= 1 && (\r\n              <div className=\"no-users-message\">\r\n                No other players yet. Invite your friends!\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Refresh confirmation dialog */}\r\n      {showRefreshConfirmation && (\r\n        <div className=\"confirmation-overlay\">\r\n          <div className=\"confirmation-dialog\">\r\n            <h3>Warning!</h3>\r\n            <p>\r\n              Refreshing your board will delete your previous one and restart your progress.\r\n              Are you sure you want to continue?\r\n            </p>\r\n            <div className=\"confirmation-buttons\">\r\n              <button onClick={handleCancelRefresh} className=\"cancel-button\">\r\n                Cancel\r\n              </button>\r\n              <button onClick={handleConfirmRefresh} className=\"confirm-button\">\r\n                Yes, Refresh My Board\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Agreement modal */}\r\n      {showAgreementModal && (\r\n        <div className=\"confirmation-overlay\">\r\n          <div className=\"agreement-modal\">\r\n            <h3>Before You Start Playing</h3>\r\n            <div className=\"agreement-text\">\r\n              <p>\r\n                Before playing or making any complaints, you must read the <a href=\"/how-to-play\" target=\"_blank\" rel=\"noopener noreferrer\">How to Play</a> section and the <a href=\"#rules\">Rules</a> to understand the game mechanics, scoring system, and proper etiquette.\r\n              </p>\r\n            </div>\r\n            <div className=\"agreement-checkbox-section\">\r\n              <label className=\"agreement-checkbox\">\r\n                <input\r\n                  type=\"checkbox\"\r\n                  checked={hasAgreedToTerms}\r\n                  onChange={(e) => setHasAgreedToTerms(e.target.checked)}\r\n                />\r\n                I promise\r\n              </label>\r\n            </div>\r\n            <div className=\"confirmation-buttons\">\r\n              <button onClick={handleAgreementCancel} className=\"cancel-button\">\r\n                Cancel\r\n              </button>\r\n              <button\r\n                onClick={handleAgreementConfirm}\r\n                className={`confirm-button ${hasAgreedToTerms ? 'enabled' : 'disabled'}`}\r\n                disabled={!hasAgreedToTerms}\r\n              >\r\n                Start Playing!\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* How to Play and Rules sections */}\r\n      <div className=\"dashboard-info-sections\">\r\n        <Rules />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default UserDashboard;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,KAAK,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B;EACA,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGR,OAAO,CAAC,CAAC;EAClC,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACS,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACc,SAAS,EAAEC,YAAY,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACgB,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACoB,uBAAuB,EAAEC,0BAA0B,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EAC7E,MAAM,CAACsB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMyB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACF;QACA,MAAMC,aAAa,GAAG,MAAMC,KAAK,CAAC,iCAAiC,CAAC;QAEpE,IAAI,CAACD,aAAa,CAACE,EAAE,EAAE;UACrB,MAAM,IAAIC,KAAK,CAAC,uBAAuBH,aAAa,CAACI,MAAM,EAAE,CAAC;QAChE;QAEA,MAAMC,SAAS,GAAG,MAAML,aAAa,CAACM,IAAI,CAAC,CAAC;QAC5CpB,QAAQ,CAACmB,SAAS,CAAC;;QAEnB;QACA,IAAIvB,IAAI,EAAE;UACR,MAAMyB,aAAa,GAAG,MAAMN,KAAK,CAAC,mCAAmCnB,IAAI,CAAC0B,EAAE,QAAQ,EAAE;YACpFC,MAAM,EAAE,KAAK;YACbC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;UAEF,IAAIH,aAAa,CAACL,EAAE,EAAE;YACpB,MAAMS,SAAS,GAAG,MAAMJ,aAAa,CAACD,IAAI,CAAC,CAAC;YAC5ClB,YAAY,CAACuB,SAAS,CAAC;YACvB;YACA3B,QAAQ,CAAC,WAAW4B,kBAAkB,CAAC9B,IAAI,CAAC+B,YAAY,CAAC,EAAE,CAAC;YAC5D,OAAO,CAAC;UACV,CAAC,MAAM,IAAIN,aAAa,CAACH,MAAM,KAAK,GAAG,EAAE;YACvC;YACA,MAAM,IAAID,KAAK,CAAC,uBAAuBI,aAAa,CAACH,MAAM,EAAE,CAAC;UAChE;QACF;MACF,CAAC,CAAC,OAAOU,GAAG,EAAE;QACZC,OAAO,CAACxB,KAAK,CAAC,sBAAsB,EAAEuB,GAAG,CAAC;QAC1CtB,QAAQ,CAAC,8CAA8C,CAAC;MAC1D,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDS,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,CAACjB,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMkC,YAAY,GAAGA,CAAA,KAAM;IACzBjC,MAAM,CAAC,CAAC;IACRC,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;;EAED;EACA,MAAMiC,WAAW,GAAGA,CAAA,KAAM;IACxBjC,QAAQ,CAAC,WAAW4B,kBAAkB,CAAC9B,IAAI,CAAC+B,YAAY,CAAC,EAAE,CAAC;EAC9D,CAAC;;EAED;EACA,MAAMK,aAAa,GAAIC,WAAW,IAAK;IACrCnC,QAAQ,CAAC,WAAW4B,kBAAkB,CAACO,WAAW,CAAC,EAAE,CAAC;EACxD,CAAC;;EAED;EACA,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;IACpCxB,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMyB,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI,CAACxB,gBAAgB,EAAE;MACrBL,QAAQ,CAAC,2EAA2E,CAAC;MACrF;IACF;IACAI,qBAAqB,CAAC,KAAK,CAAC;IAC5B0B,cAAc,CAAC,CAAC;EAClB,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC3B,qBAAqB,CAAC,KAAK,CAAC;IAC5BE,mBAAmB,CAAC,KAAK,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMwB,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI;MACFhC,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMgC,QAAQ,GAAG,MAAMvB,KAAK,CAAC,mCAAmCnB,IAAI,CAAC0B,EAAE,QAAQ,EAAE;QAC/EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACc,QAAQ,CAACtB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBqB,QAAQ,CAACpB,MAAM,EAAE,CAAC;MAC3D;;MAEA;MACA,MAAMO,SAAS,GAAG,MAAMa,QAAQ,CAAClB,IAAI,CAAC,CAAC;MACvClB,YAAY,CAACuB,SAAS,CAAC;;MAEvB;MACA3B,QAAQ,CAAC,WAAW4B,kBAAkB,CAAC9B,IAAI,CAAC+B,YAAY,CAAC,EAAE,CAAC;IAC9D,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACxB,KAAK,CAAC,2BAA2B,EAAEuB,GAAG,CAAC;MAC/CtB,QAAQ,CAAC,qDAAqD,CAAC;IACjE,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B/B,0BAA0B,CAAC,IAAI,CAAC;EAClC,CAAC;;EAED;EACA,MAAMgC,mBAAmB,GAAGA,CAAA,KAAM;IAChChC,0BAA0B,CAAC,KAAK,CAAC;EACnC,CAAC;;EAED;EACA,MAAMiC,oBAAoB,GAAG,MAAAA,CAAA,KAAY;IACvC,IAAI;MACFrC,UAAU,CAAC,IAAI,CAAC;MAChBI,0BAA0B,CAAC,KAAK,CAAC;MAEjC,MAAM8B,QAAQ,GAAG,MAAMvB,KAAK,CAAC,mCAAmCnB,IAAI,CAAC0B,EAAE,QAAQ,EAAE;QAC/EC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,IAAI,CAACc,QAAQ,CAACtB,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBqB,QAAQ,CAACpB,MAAM,EAAE,CAAC;MAC3D;;MAEA;MACA,MAAMO,SAAS,GAAG,MAAMa,QAAQ,CAAClB,IAAI,CAAC,CAAC;MACvClB,YAAY,CAACuB,SAAS,CAAC;;MAEvB;MACA3B,QAAQ,CAAC,WAAW4B,kBAAkB,CAAC9B,IAAI,CAAC+B,YAAY,CAAC,EAAE,CAAC;IAC9D,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZC,OAAO,CAACxB,KAAK,CAAC,yBAAyB,EAAEuB,GAAG,CAAC;MAC7CtB,QAAQ,CAAC,kDAAkD,CAAC;IAC9D,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,IAAI,CAACR,IAAI,EAAE;IACTE,QAAQ,CAAC,GAAG,CAAC;IACb,OAAO,IAAI;EACb;EAEA,oBACEL,OAAA;IAAKiD,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClClD,OAAA;MAAKiD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BlD,OAAA;QAAAkD,QAAA,GAAI,WAAS,EAAC/C,IAAI,CAAC+B,YAAY,EAAC,GAAC;MAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACtCtD,OAAA;QAAQuD,OAAO,EAAElB,YAAa;QAACY,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAAM;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eAENtD,OAAA;MAAKiD,SAAS,EAAC,mBAAmB;MAAAC,QAAA,EAC/BxC,OAAO,gBACNV,OAAA;QAAKiD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GAC/C,CAAC9C,SAAS,gBACZR,OAAA;QAAKiD,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrClD,OAAA;UAAAkD,QAAA,EAAI;QAAuB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChCtD,OAAA;UACEuD,OAAO,EAAEd,uBAAwB;UACjCQ,SAAS,EAAC,8BAA8B;UACxCO,QAAQ,EAAE9C,OAAQ;UAAAwC,QAAA,EACnB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,GACJ;IAAI;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEL1C,KAAK,iBAAIZ,OAAA;MAAKiD,SAAS,EAAC,eAAe;MAAAC,QAAA,EAAEtC;IAAK;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEtDtD,OAAA;MAAKiD,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BlD,OAAA;QAAAkD,QAAA,EAAI;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACrB5C,OAAO,gBACNV,OAAA;QAAKiD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gBAEvDtD,OAAA;QAAKiD,SAAS,EAAC,YAAY;QAAAC,QAAA,GACxB5C,KAAK,CACHmD,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC7B,EAAE,KAAK1B,IAAI,CAAC0B,EAAE,CAAC,CAAC;QAAA,CAC9B8B,GAAG,CAACD,CAAC,iBACJ1D,OAAA;UAAgBiD,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACnClD,OAAA;YAAMiD,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAEQ,CAAC,CAACxB;UAAY;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDtD,OAAA;YACEuD,OAAO,EAAEA,CAAA,KAAMhB,aAAa,CAACmB,CAAC,CAACxB,YAAY,CAAE;YAC7Ce,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC9B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,GAPDI,CAAC,CAAC7B,EAAE;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQT,CACN,CAAC,EACHhD,KAAK,CAACsD,MAAM,IAAI,CAAC,iBAChB5D,OAAA;UAAKiD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLxC,uBAAuB,iBACtBd,OAAA;MAAKiD,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnClD,OAAA;QAAKiD,SAAS,EAAC,qBAAqB;QAAAC,QAAA,gBAClClD,OAAA;UAAAkD,QAAA,EAAI;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjBtD,OAAA;UAAAkD,QAAA,EAAG;QAGH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJtD,OAAA;UAAKiD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnClD,OAAA;YAAQuD,OAAO,EAAER,mBAAoB;YAACE,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAEhE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtD,OAAA;YAAQuD,OAAO,EAAEP,oBAAqB;YAACC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAtC,kBAAkB,iBACjBhB,OAAA;MAAKiD,SAAS,EAAC,sBAAsB;MAAAC,QAAA,eACnClD,OAAA;QAAKiD,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BlD,OAAA;UAAAkD,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjCtD,OAAA;UAAKiD,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BlD,OAAA;YAAAkD,QAAA,GAAG,6DAC0D,eAAAlD,OAAA;cAAG6D,IAAI,EAAC,cAAc;cAACC,MAAM,EAAC,QAAQ;cAACC,GAAG,EAAC,qBAAqB;cAAAb,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,qBAAiB,eAAAtD,OAAA;cAAG6D,IAAI,EAAC,QAAQ;cAAAX,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,4EACxL;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNtD,OAAA;UAAKiD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzClD,OAAA;YAAOiD,SAAS,EAAC,oBAAoB;YAAAC,QAAA,gBACnClD,OAAA;cACEgE,IAAI,EAAC,UAAU;cACfC,OAAO,EAAE/C,gBAAiB;cAC1BgD,QAAQ,EAAGC,CAAC,IAAKhD,mBAAmB,CAACgD,CAAC,CAACL,MAAM,CAACG,OAAO;YAAE;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,aAEJ;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNtD,OAAA;UAAKiD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnClD,OAAA;YAAQuD,OAAO,EAAEX,qBAAsB;YAACK,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAElE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTtD,OAAA;YACEuD,OAAO,EAAEb,sBAAuB;YAChCO,SAAS,EAAE,kBAAkB/B,gBAAgB,GAAG,SAAS,GAAG,UAAU,EAAG;YACzEsC,QAAQ,EAAE,CAACtC,gBAAiB;YAAAgC,QAAA,EAC7B;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDtD,OAAA;MAAKiD,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtClD,OAAA,CAACF,KAAK;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACpD,EAAA,CAnSID,aAAa;EAAA,QAEQL,OAAO,EACfC,WAAW;AAAA;AAAAuE,EAAA,GAHxBnE,aAAa;AAqSnB,eAAeA,aAAa;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}