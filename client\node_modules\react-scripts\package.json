{"_from": "react-scripts@5.0.1", "_id": "react-scripts@5.0.1", "_inBundle": false, "_integrity": "sha512-8VAmEm/ZAwQzJ+GOMLbBsTdDKOpuZh7RPs0UymvBR2vRk4iZWCskjbFnxqjrzoIvlNNRZ3QJFx6/qDSi6zSnaQ==", "_location": "/react-scripts", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "react-scripts@5.0.1", "name": "react-scripts", "escapedName": "react-scripts", "rawSpec": "5.0.1", "saveSpec": null, "fetchSpec": "5.0.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/react-scripts/-/react-scripts-5.0.1.tgz", "_shasum": "6285dbd65a8ba6e49ca8d651ce30645a6d980003", "_spec": "react-scripts@5.0.1", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client", "bin": {"react-scripts": "bin/react-scripts.js"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "bugs": {"url": "https://github.com/facebook/create-react-app/issues"}, "bundleDependencies": false, "dependencies": {"@babel/core": "^7.16.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.3", "@svgr/webpack": "^5.5.0", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "bfj": "^7.0.2", "browserslist": "^4.18.1", "camelcase": "^6.2.1", "case-sensitive-paths-webpack-plugin": "^2.4.0", "css-loader": "^6.5.1", "css-minimizer-webpack-plugin": "^3.2.0", "dotenv": "^10.0.0", "dotenv-expand": "^5.1.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-webpack-plugin": "^3.1.1", "file-loader": "^6.2.0", "fs-extra": "^10.0.0", "fsevents": "^2.3.2", "html-webpack-plugin": "^5.5.0", "identity-obj-proxy": "^3.0.0", "jest": "^27.4.3", "jest-resolve": "^27.4.2", "jest-watch-typeahead": "^1.0.0", "mini-css-extract-plugin": "^2.4.5", "postcss": "^8.4.4", "postcss-flexbugs-fixes": "^5.0.2", "postcss-loader": "^6.2.1", "postcss-normalize": "^10.0.1", "postcss-preset-env": "^7.0.1", "prompts": "^2.4.2", "react-app-polyfill": "^3.0.0", "react-dev-utils": "^12.0.1", "react-refresh": "^0.11.0", "resolve": "^1.20.0", "resolve-url-loader": "^4.0.0", "sass-loader": "^12.3.0", "semver": "^7.3.5", "source-map-loader": "^3.0.0", "style-loader": "^3.3.1", "tailwindcss": "^3.0.2", "terser-webpack-plugin": "^5.2.5", "webpack": "^5.64.4", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "^4.0.2", "workbox-webpack-plugin": "^6.4.1"}, "deprecated": false, "description": "Configuration and scripts for Create React App.", "devDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "engines": {"node": ">=14.0.0"}, "files": ["bin", "config", "lib", "scripts", "template", "template-typescript", "utils"], "gitHead": "19fa58d527ae74f2b6baa0867463eea1d290f9a5", "homepage": "https://github.com/facebook/create-react-app#readme", "license": "MIT", "name": "react-scripts", "optionalDependencies": {"fsevents": "^2.3.2"}, "peerDependencies": {"react": ">= 16", "typescript": "^3.2.1 || ^4"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/facebook/create-react-app.git", "directory": "packages/react-scripts"}, "types": "./lib/react-app.d.ts", "version": "5.0.1"}