{"_from": "p-locate@^5.0.0", "_id": "p-locate@5.0.0", "_inBundle": false, "_integrity": "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==", "_location": "/react-dev-utils/p-locate", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "p-locate@^5.0.0", "name": "p-locate", "escapedName": "p-locate", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/react-dev-utils/locate-path"], "_resolved": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz", "_shasum": "83c8315c6785005e3bd021839411c9e110e6d834", "_spec": "p-locate@^5.0.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\node_modules\\react-dev-utils\\node_modules\\locate-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-locate/issues"}, "bundleDependencies": false, "dependencies": {"p-limit": "^3.0.2"}, "deprecated": false, "description": "Get the first fulfilled promise that satisfies the provided testing function", "devDependencies": {"ava": "^2.4.0", "delay": "^4.1.0", "in-range": "^2.0.0", "time-span": "^4.0.0", "tsd": "^0.13.1", "xo": "^0.32.1"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/p-locate#readme", "keywords": ["promise", "locate", "find", "finder", "search", "searcher", "test", "array", "collection", "iterable", "iterator", "race", "fulfilled", "fastest", "async", "await", "promises", "bluebird"], "license": "MIT", "name": "p-locate", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-locate.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "5.0.0"}