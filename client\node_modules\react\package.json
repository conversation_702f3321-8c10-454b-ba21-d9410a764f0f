{"_from": "react@^19.1.0", "_id": "react@19.1.0", "_inBundle": false, "_integrity": "sha512-FS+XFBNvn3GTAWq26joslQgWNoFu08F4kl0J4CgdNKADkdSGXQyTCnKteIAJy96Br6YbpEU1LSzV5dYtjMkMDg==", "_location": "/react", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "react@^19.1.0", "name": "react", "escapedName": "react", "rawSpec": "^19.1.0", "saveSpec": null, "fetchSpec": "^19.1.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/react/-/react-19.1.0.tgz", "_shasum": "926864b6c48da7627f004795d6cce50e90793b75", "_spec": "react@^19.1.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client", "bugs": {"url": "https://github.com/facebook/react/issues"}, "bundleDependencies": false, "deprecated": false, "description": "React is a JavaScript library for building user interfaces.", "engines": {"node": ">=0.10.0"}, "exports": {".": {"react-server": "./react.react-server.js", "default": "./index.js"}, "./package.json": "./package.json", "./jsx-runtime": {"react-server": "./jsx-runtime.react-server.js", "default": "./jsx-runtime.js"}, "./jsx-dev-runtime": {"react-server": "./jsx-dev-runtime.react-server.js", "default": "./jsx-dev-runtime.js"}, "./compiler-runtime": {"react-server": "./compiler-runtime.js", "default": "./compiler-runtime.js"}}, "files": ["LICENSE", "README.md", "index.js", "cjs/", "compiler-runtime.js", "jsx-runtime.js", "jsx-runtime.react-server.js", "jsx-dev-runtime.js", "jsx-dev-runtime.react-server.js", "react.react-server.js"], "homepage": "https://react.dev/", "keywords": ["react"], "license": "MIT", "main": "index.js", "name": "react", "repository": {"type": "git", "url": "git+https://github.com/facebook/react.git", "directory": "packages/react"}, "version": "19.1.0"}