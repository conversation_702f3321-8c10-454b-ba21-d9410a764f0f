{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Board\\\\PointsDisplay.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PointsDisplay = ({\n  characters,\n  markedCells,\n  onRefreshClick,\n  isReadOnly,\n  score\n}) => {\n  // Point values for each rarity\n  const RARITY_POINTS = {\n    \"FREE\": 1,\n    \"R\": 2,\n    \"SR\": 3,\n    \"SSR\": 4,\n    \"UR+\": 6\n  };\n\n  // Calculate points from marked cells\n  const calculateBasePoints = () => {\n    let totalPoints = 0;\n    markedCells.forEach(index => {\n      if (index >= 0 && index < characters.length) {\n        const character = characters[index];\n        totalPoints += RARITY_POINTS[character.rarity];\n      }\n    });\n    return totalPoints;\n  };\n\n  // Check if there's a bingo (5 in a row, column, or diagonal)\n  const checkForBingos = () => {\n    const bingoBonus = 5; // Bonus points for each bingo\n    let bingoCount = 0;\n\n    // Convert markedCells set to a 5x5 grid for easier checking\n    const grid = Array(5).fill().map(() => Array(5).fill(false));\n    markedCells.forEach(index => {\n      const row = Math.floor(index / 5);\n      const col = index % 5;\n      grid[row][col] = true;\n    });\n\n    // Check rows\n    for (let row = 0; row < 5; row++) {\n      if (grid[row].every(cell => cell)) {\n        bingoCount++;\n      }\n    }\n\n    // Check columns\n    for (let col = 0; col < 5; col++) {\n      if (grid.every(row => row[col])) {\n        bingoCount++;\n      }\n    }\n\n    // Check main diagonal (top-left to bottom-right)\n    if (grid[0][0] && grid[1][1] && grid[2][2] && grid[3][3] && grid[4][4]) {\n      bingoCount++;\n    }\n\n    // Check other diagonal (top-right to bottom-left)\n    if (grid[0][4] && grid[1][3] && grid[2][2] && grid[3][1] && grid[4][0]) {\n      bingoCount++;\n    }\n    return bingoCount * bingoBonus;\n  };\n  const basePoints = calculateBasePoints();\n  const bingoPoints = checkForBingos();\n  const totalPoints = basePoints + bingoPoints;\n\n  // Use the score from props if provided, otherwise calculate it\n  const displayScore = score !== undefined ? score : totalPoints;\n\n  // Calculate the percentage for the meter (0-500 points)\n  const MAX_POINTS = 500; // Updated to 500 as per requirements\n  const fillPercentage = Math.min(displayScore / MAX_POINTS * 100, 100);\n\n  // Milestone definitions - updated as per requirements\n  const milestones = [{\n    points: 150,\n    reward: \"Toast in L.A. Live!\"\n  }, {\n    points: 250,\n    reward: \"Mystery prize!\"\n  }, {\n    points: 350,\n    reward: \"Yakitori!\"\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"points-controls-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"points-meter-container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"points-meter\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"points-meter-fill\",\n          style: {\n            width: `${fillPercentage}%`\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"progress-text\",\n          children: \"GROUP POINTS\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), milestones.map((milestone, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"milestone\",\n          style: {\n            left: `${milestone.points / MAX_POINTS * 100}%`\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"milestone-tooltip\",\n            children: milestone.reward\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"milestone-value\",\n            children: milestone.points\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"controls-wrapper\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"points-display\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"score-label\",\n          children: \"Score\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"score-value\",\n          children: displayScore\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 114,\n        columnNumber: 9\n      }, this), !isReadOnly && /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"refresh-button\",\n        onClick: onRefreshClick,\n        title: \"Refresh Board\",\n        children: \"\\u21BB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n};\n_c = PointsDisplay;\nexport default PointsDisplay;\nvar _c;\n$RefreshReg$(_c, \"PointsDisplay\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "PointsDisplay", "characters", "<PERSON><PERSON><PERSON><PERSON>", "onRefreshClick", "isReadOnly", "score", "RARITY_POINTS", "calculateBasePoints", "totalPoints", "for<PERSON>ach", "index", "length", "character", "rarity", "checkForBingos", "bingoBonus", "bingoCount", "grid", "Array", "fill", "map", "row", "Math", "floor", "col", "every", "cell", "basePoints", "bingoPoints", "displayScore", "undefined", "MAX_POINTS", "fillPercentage", "min", "milestones", "points", "reward", "className", "children", "style", "width", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "milestone", "left", "onClick", "title", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/PointsDisplay.js"], "sourcesContent": ["import React from 'react';\r\n\r\nconst PointsDisplay = ({ characters, markedCells, onRefreshClick, isReadOnly, score }) => {\r\n  // Point values for each rarity\r\n  const RARITY_POINTS = {\r\n    \"FREE\": 1,\r\n    \"R\": 2,\r\n    \"SR\": 3,\r\n    \"SSR\": 4,\r\n    \"UR+\": 6\r\n  };\r\n\r\n  // Calculate points from marked cells\r\n  const calculateBasePoints = () => {\r\n    let totalPoints = 0;\r\n    markedCells.forEach(index => {\r\n      if (index >= 0 && index < characters.length) {\r\n        const character = characters[index];\r\n        totalPoints += RARITY_POINTS[character.rarity];\r\n      }\r\n    });\r\n    return totalPoints;\r\n  };\r\n\r\n  // Check if there's a bingo (5 in a row, column, or diagonal)\r\n  const checkForBingos = () => {\r\n    const bingoBonus = 5; // Bonus points for each bingo\r\n    let bingoCount = 0;\r\n\r\n    // Convert markedCells set to a 5x5 grid for easier checking\r\n    const grid = Array(5).fill().map(() => Array(5).fill(false));\r\n    markedCells.forEach(index => {\r\n      const row = Math.floor(index / 5);\r\n      const col = index % 5;\r\n      grid[row][col] = true;\r\n    });\r\n\r\n    // Check rows\r\n    for (let row = 0; row < 5; row++) {\r\n      if (grid[row].every(cell => cell)) {\r\n        bingoCount++;\r\n      }\r\n    }\r\n\r\n    // Check columns\r\n    for (let col = 0; col < 5; col++) {\r\n      if (grid.every(row => row[col])) {\r\n        bingoCount++;\r\n      }\r\n    }\r\n\r\n    // Check main diagonal (top-left to bottom-right)\r\n    if (grid[0][0] && grid[1][1] && grid[2][2] && grid[3][3] && grid[4][4]) {\r\n      bingoCount++;\r\n    }\r\n\r\n    // Check other diagonal (top-right to bottom-left)\r\n    if (grid[0][4] && grid[1][3] && grid[2][2] && grid[3][1] && grid[4][0]) {\r\n      bingoCount++;\r\n    }\r\n\r\n    return bingoCount * bingoBonus;\r\n  };\r\n\r\n  const basePoints = calculateBasePoints();\r\n  const bingoPoints = checkForBingos();\r\n  const totalPoints = basePoints + bingoPoints;\r\n\r\n  // Use the score from props if provided, otherwise calculate it\r\n  const displayScore = score !== undefined ? score : totalPoints;\r\n\r\n  // Calculate the percentage for the meter (0-500 points)\r\n  const MAX_POINTS = 500; // Updated to 500 as per requirements\r\n  const fillPercentage = Math.min((displayScore / MAX_POINTS) * 100, 100);\r\n\r\n  // Milestone definitions - updated as per requirements\r\n  const milestones = [\r\n    { points: 150, reward: \"Toast in L.A. Live!\" },\r\n    { points: 250, reward: \"Mystery prize!\" },\r\n    { points: 350, reward: \"Yakitori!\" }\r\n  ];\r\n\r\n  return (\r\n    <div className=\"points-controls-container\">\r\n      <div className=\"points-meter-container\">\r\n        <div className=\"points-meter\">\r\n          <div\r\n            className=\"points-meter-fill\"\r\n            style={{ width: `${fillPercentage}%` }}\r\n          ></div>\r\n\r\n          {/* Add the progress text with negative coloring effect */}\r\n          <div className=\"progress-text\">\r\n            GROUP POINTS\r\n          </div>\r\n\r\n          {/* Render milestones */}\r\n          {milestones.map((milestone, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"milestone\"\r\n              style={{ left: `${(milestone.points / MAX_POINTS) * 100}%` }}\r\n            >\r\n              <div className=\"milestone-tooltip\">\r\n                {milestone.reward}\r\n              </div>\r\n              <div className=\"milestone-value\">{milestone.points}</div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"controls-wrapper\">\r\n        <div className=\"points-display\">\r\n          <div className=\"score-label\">Score</div>\r\n          <div className=\"score-value\">{displayScore}</div>\r\n        </div>\r\n        {!isReadOnly && (\r\n          <button \r\n            className=\"refresh-button\" \r\n            onClick={onRefreshClick} \r\n            title=\"Refresh Board\"\r\n          >\r\n            ↻\r\n          </button>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PointsDisplay;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,aAAa,GAAGA,CAAC;EAAEC,UAAU;EAAEC,WAAW;EAAEC,cAAc;EAAEC,UAAU;EAAEC;AAAM,CAAC,KAAK;EACxF;EACA,MAAMC,aAAa,GAAG;IACpB,MAAM,EAAE,CAAC;IACT,GAAG,EAAE,CAAC;IACN,IAAI,EAAE,CAAC;IACP,KAAK,EAAE,CAAC;IACR,KAAK,EAAE;EACT,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,IAAIC,WAAW,GAAG,CAAC;IACnBN,WAAW,CAACO,OAAO,CAACC,KAAK,IAAI;MAC3B,IAAIA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGT,UAAU,CAACU,MAAM,EAAE;QAC3C,MAAMC,SAAS,GAAGX,UAAU,CAACS,KAAK,CAAC;QACnCF,WAAW,IAAIF,aAAa,CAACM,SAAS,CAACC,MAAM,CAAC;MAChD;IACF,CAAC,CAAC;IACF,OAAOL,WAAW;EACpB,CAAC;;EAED;EACA,MAAMM,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,UAAU,GAAG,CAAC,CAAC,CAAC;IACtB,IAAIC,UAAU,GAAG,CAAC;;IAElB;IACA,MAAMC,IAAI,GAAGC,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAACC,GAAG,CAAC,MAAMF,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5DjB,WAAW,CAACO,OAAO,CAACC,KAAK,IAAI;MAC3B,MAAMW,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACb,KAAK,GAAG,CAAC,CAAC;MACjC,MAAMc,GAAG,GAAGd,KAAK,GAAG,CAAC;MACrBO,IAAI,CAACI,GAAG,CAAC,CAACG,GAAG,CAAC,GAAG,IAAI;IACvB,CAAC,CAAC;;IAEF;IACA,KAAK,IAAIH,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;MAChC,IAAIJ,IAAI,CAACI,GAAG,CAAC,CAACI,KAAK,CAACC,IAAI,IAAIA,IAAI,CAAC,EAAE;QACjCV,UAAU,EAAE;MACd;IACF;;IAEA;IACA,KAAK,IAAIQ,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAG,CAAC,EAAEA,GAAG,EAAE,EAAE;MAChC,IAAIP,IAAI,CAACQ,KAAK,CAACJ,GAAG,IAAIA,GAAG,CAACG,GAAG,CAAC,CAAC,EAAE;QAC/BR,UAAU,EAAE;MACd;IACF;;IAEA;IACA,IAAIC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtED,UAAU,EAAE;IACd;;IAEA;IACA,IAAIC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACtED,UAAU,EAAE;IACd;IAEA,OAAOA,UAAU,GAAGD,UAAU;EAChC,CAAC;EAED,MAAMY,UAAU,GAAGpB,mBAAmB,CAAC,CAAC;EACxC,MAAMqB,WAAW,GAAGd,cAAc,CAAC,CAAC;EACpC,MAAMN,WAAW,GAAGmB,UAAU,GAAGC,WAAW;;EAE5C;EACA,MAAMC,YAAY,GAAGxB,KAAK,KAAKyB,SAAS,GAAGzB,KAAK,GAAGG,WAAW;;EAE9D;EACA,MAAMuB,UAAU,GAAG,GAAG,CAAC,CAAC;EACxB,MAAMC,cAAc,GAAGV,IAAI,CAACW,GAAG,CAAEJ,YAAY,GAAGE,UAAU,GAAI,GAAG,EAAE,GAAG,CAAC;;EAEvE;EACA,MAAMG,UAAU,GAAG,CACjB;IAAEC,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAsB,CAAC,EAC9C;IAAED,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAiB,CAAC,EACzC;IAAED,MAAM,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAY,CAAC,CACrC;EAED,oBACErC,OAAA;IAAKsC,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCvC,OAAA;MAAKsC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCvC,OAAA;QAAKsC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BvC,OAAA;UACEsC,SAAS,EAAC,mBAAmB;UAC7BE,KAAK,EAAE;YAAEC,KAAK,EAAE,GAAGR,cAAc;UAAI;QAAE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAGP7C,OAAA;UAAKsC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE/B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAGLV,UAAU,CAACd,GAAG,CAAC,CAACyB,SAAS,EAAEnC,KAAK,kBAC/BX,OAAA;UAEEsC,SAAS,EAAC,WAAW;UACrBE,KAAK,EAAE;YAAEO,IAAI,EAAE,GAAID,SAAS,CAACV,MAAM,GAAGJ,UAAU,GAAI,GAAG;UAAI,CAAE;UAAAO,QAAA,gBAE7DvC,OAAA;YAAKsC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAC/BO,SAAS,CAACT;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC,eACN7C,OAAA;YAAKsC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAEO,SAAS,CAACV;UAAM;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAPpDlC,KAAK;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAEN7C,OAAA;MAAKsC,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BvC,OAAA;QAAKsC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BvC,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAK;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACxC7C,OAAA;UAAKsC,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAET;QAAY;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC,EACL,CAACxC,UAAU,iBACVL,OAAA;QACEsC,SAAS,EAAC,gBAAgB;QAC1BU,OAAO,EAAE5C,cAAe;QACxB6C,KAAK,EAAC,eAAe;QAAAV,QAAA,EACtB;MAED;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACK,EAAA,GA/HIjD,aAAa;AAiInB,eAAeA,aAAa;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}