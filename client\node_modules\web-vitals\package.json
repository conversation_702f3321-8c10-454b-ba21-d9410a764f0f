{"_from": "web-vitals@^2.1.4", "_id": "web-vitals@2.1.4", "_inBundle": false, "_integrity": "sha512-sVWcwhU5mX6crfI5Vd2dC4qchyTqxV8URinzt25XqVh+bHEPGH4C3NPrNionCP7Obx59wrYEbNlw4Z8sjALzZg==", "_location": "/web-vitals", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "web-vitals@^2.1.4", "name": "web-vitals", "escapedName": "web-vitals", "rawSpec": "^2.1.4", "saveSpec": null, "fetchSpec": "^2.1.4"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/web-vitals/-/web-vitals-2.1.4.tgz", "_shasum": "76563175a475a5e835264d373704f9dde718290c", "_spec": "web-vitals@^2.1.4", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://philipwalton.com"}, "bugs": {"url": "https://github.com/GoogleChrome/web-vitals/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Easily measure performance metrics in JavaScript", "devDependencies": {"@babel/core": "^7.16.10", "@babel/preset-env": "^7.16.11", "@rollup/plugin-replace": "^3.0.1", "@typescript-eslint/eslint-plugin": "^5.10.0", "@typescript-eslint/parser": "^5.10.0", "@wdio/cli": "^7.16.13", "@wdio/local-runner": "^7.16.13", "@wdio/mocha-framework": "^7.16.13", "@wdio/selenium-standalone-service": "^7.16.13", "@wdio/spec-reporter": "^7.16.13", "body-parser": "^1.19.1", "chromedriver": "^97.0.0", "eslint": "^8.7.0", "eslint-config-google": "^0.14.0", "express": "^4.17.2", "fs-extra": "^10.0.0", "husky": "^7.0.4", "npm-run-all": "^4.1.5", "nunjucks": "^3.2.3", "rollup": "^2.64.0", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^7.0.2", "typescript": "^4.5.5", "wdio-chromedriver-service": "^7.2.6"}, "files": ["base.js", "base.d.ts", "dist", "src"], "homepage": "https://github.com/GoogleChrome/web-vitals#readme", "husky": {"hooks": {"pre-commit": "npm run lint"}}, "keywords": ["crux", "performance", "metrics", "CLS", "FCP", "FID", "LCP", "TTFB"], "license": "Apache-2.0", "main": "dist/web-vitals.umd.js", "module": "dist/web-vitals.js", "name": "web-vitals", "repository": {"type": "git", "url": "git+https://github.com/GoogleChrome/web-vitals.git"}, "scripts": {"build": "run-s clean build:ts build:js", "build:js": "rollup -c", "build:ts": "tsc -b", "clean": "rm -rf dist tsconfig.tsbuildinfo", "dev": "run-p watch test:server", "lint": "eslint \"*.js\" \"src/**/*.ts\" \"test/**/*.js\"", "lint:fix": "eslint --fix \"*.js\" \"src/**/*.ts\" \"test/**/*.js\"", "postversion": "git push --follow-tags", "prepare": "husky install", "release:major": "npm version major -m 'Release v%s' && npm publish", "release:minor": "npm version minor -m 'Release v%s' && npm publish", "release:patch": "npm version patch -m 'Release v%s' && npm publish", "start": "run-s build:ts test:server watch", "test": "npm-run-all build -p -r test:*", "test:e2e": "wdio wdio.conf.js", "test:server": "node test/server.js", "version": "run-s build", "watch": "run-p watch:*", "watch:js": "rollup -c -w", "watch:ts": "tsc -b -w"}, "typings": "dist/modules/index.d.ts", "version": "2.1.4"}