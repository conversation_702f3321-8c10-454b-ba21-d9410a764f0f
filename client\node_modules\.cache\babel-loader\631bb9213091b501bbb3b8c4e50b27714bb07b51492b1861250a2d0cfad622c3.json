{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Board\\\\BoardViewer.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams, useNavigate } from 'react-router-dom';\nimport { useUser } from '../Auth/UserContext';\n\n// Import components from App.js (we'll extract these later)\nimport BingoBoard from './BingoBoard';\nimport Leaderboard from './Leaderboard';\nimport { Rules } from '../../App';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst BoardViewer = () => {\n  _s();\n  // Get display name from URL params\n  const {\n    displayName\n  } = useParams();\n  const navigate = useNavigate();\n\n  // Get current user from context\n  const {\n    user\n  } = useUser();\n\n  // State for board data and owner info\n  const [boardOwner, setBoardOwner] = useState(null);\n  const [board, setBoard] = useState(null);\n  const [progress, setProgress] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Determine if this is the current user's board\n  const isOwnBoard = user && displayName.toLowerCase() === user.display_name.toLowerCase();\n\n  // Fetch board data and owner info\n  useEffect(() => {\n    const fetchBoardData = async () => {\n      try {\n        setLoading(true);\n\n        // Fetch the user's board by display name\n        const boardResponse = await fetch(`http://localhost:5000/api/boards/${encodeURIComponent(displayName)}`);\n        if (!boardResponse.ok) {\n          if (boardResponse.status === 404) {\n            throw new Error('User not found or no board exists for this user');\n          }\n          throw new Error(`HTTP error! Status: ${boardResponse.status}`);\n        }\n        const boardData = await boardResponse.json();\n        setBoard(boardData.board);\n        setBoardOwner(boardData.user);\n\n        // Fetch the board progress\n        const progressResponse = await fetch(`http://localhost:5000/api/boards/${encodeURIComponent(displayName)}/progress`);\n        if (!progressResponse.ok) {\n          throw new Error(`HTTP error! Status: ${progressResponse.status}`);\n        }\n        const progressData = await progressResponse.json();\n        setProgress(progressData);\n      } catch (err) {\n        console.error('Error fetching board data:', err);\n        setError(err.message || 'Failed to load board data. Please try again later.');\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchBoardData();\n  }, [displayName]);\n\n  // Handle back to dashboard button\n  const handleBackToDashboard = () => {\n    navigate('/dashboard');\n  };\n\n  // If loading, show loading message\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-message\",\n      children: \"Loading board...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 12\n    }, this);\n  }\n\n  // If error, show error message\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"error-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"error-message\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleBackToDashboard,\n        className: \"back-button\",\n        children: \"Back to Dashboard\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"board-viewer-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"board-viewer-header\",\n      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: `board-title ${isOwnBoard ? 'your-board-title' : ''}`,\n        children: isOwnBoard ? 'Your Board' : `${boardOwner.display_name}'s Board`\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), board && progress && boardOwner && /*#__PURE__*/_jsxDEV(BingoBoard, {\n      boardData: board.board_data,\n      progressData: progress,\n      isReadOnly: !isOwnBoard,\n      userId: boardOwner.id,\n      boardId: board.id\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Leaderboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: handleBackToDashboard,\n      className: \"back-button\",\n      children: \"Back to Dashboard\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dashboard-info-sections\",\n      children: /*#__PURE__*/_jsxDEV(Rules, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 5\n  }, this);\n};\n_s(BoardViewer, \"4DzwFZXdvAoDAlvf7UZOx1SlBVM=\", false, function () {\n  return [useParams, useNavigate, useUser];\n});\n_c = BoardViewer;\nexport default BoardViewer;\nvar _c;\n$RefreshReg$(_c, \"BoardViewer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "useNavigate", "useUser", "BingoBoard", "Leaderboard", "Rules", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>iewer", "_s", "displayName", "navigate", "user", "boardOwner", "setBoardOwner", "board", "setBoard", "progress", "setProgress", "loading", "setLoading", "error", "setError", "isOwnBoard", "toLowerCase", "display_name", "fetchBoardData", "boardResponse", "fetch", "encodeURIComponent", "ok", "status", "Error", "boardData", "json", "progressResponse", "progressData", "err", "console", "message", "handleBackToDashboard", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "board_data", "isReadOnly", "userId", "id", "boardId", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Board/BoardViewer.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useParams, useNavigate } from 'react-router-dom';\r\nimport { useUser } from '../Auth/UserContext';\r\n\r\n// Import components from App.js (we'll extract these later)\r\nimport BingoBoard from './BingoBoard';\r\nimport Leaderboard from './Leaderboard';\r\nimport { Rules } from '../../App';\r\n\r\nconst BoardViewer = () => {\r\n  // Get display name from URL params\r\n  const { displayName } = useParams();\r\n  const navigate = useNavigate();\r\n\r\n  // Get current user from context\r\n  const { user } = useUser();\r\n\r\n  // State for board data and owner info\r\n  const [boardOwner, setBoardOwner] = useState(null);\r\n  const [board, setBoard] = useState(null);\r\n  const [progress, setProgress] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  // Determine if this is the current user's board\r\n  const isOwnBoard = user && displayName.toLowerCase() === user.display_name.toLowerCase();\r\n\r\n  // Fetch board data and owner info\r\n  useEffect(() => {\r\n    const fetchBoardData = async () => {\r\n      try {\r\n        setLoading(true);\r\n\r\n        // Fetch the user's board by display name\r\n        const boardResponse = await fetch(`http://localhost:5000/api/boards/${encodeURIComponent(displayName)}`);\r\n\r\n        if (!boardResponse.ok) {\r\n          if (boardResponse.status === 404) {\r\n            throw new Error('User not found or no board exists for this user');\r\n          }\r\n          throw new Error(`HTTP error! Status: ${boardResponse.status}`);\r\n        }\r\n\r\n        const boardData = await boardResponse.json();\r\n        setBoard(boardData.board);\r\n        setBoardOwner(boardData.user);\r\n\r\n        // Fetch the board progress\r\n        const progressResponse = await fetch(`http://localhost:5000/api/boards/${encodeURIComponent(displayName)}/progress`);\r\n\r\n        if (!progressResponse.ok) {\r\n          throw new Error(`HTTP error! Status: ${progressResponse.status}`);\r\n        }\r\n\r\n        const progressData = await progressResponse.json();\r\n        setProgress(progressData);\r\n\r\n      } catch (err) {\r\n        console.error('Error fetching board data:', err);\r\n        setError(err.message || 'Failed to load board data. Please try again later.');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchBoardData();\r\n  }, [displayName]);\r\n\r\n  // Handle back to dashboard button\r\n  const handleBackToDashboard = () => {\r\n    navigate('/dashboard');\r\n  };\r\n\r\n  // If loading, show loading message\r\n  if (loading) {\r\n    return <div className=\"loading-message\">Loading board...</div>;\r\n  }\r\n\r\n  // If error, show error message\r\n  if (error) {\r\n    return (\r\n      <div className=\"error-container\">\r\n        <div className=\"error-message\">{error}</div>\r\n        <button onClick={handleBackToDashboard} className=\"back-button\">\r\n          Back to Dashboard\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"board-viewer-container\">\r\n      <div className=\"board-viewer-header\">\r\n        <h1 className={`board-title ${isOwnBoard ? 'your-board-title' : ''}`}>\r\n          {isOwnBoard ? 'Your Board' : `${boardOwner.display_name}'s Board`}\r\n        </h1>\r\n      </div>\r\n\r\n      {board && progress && boardOwner && (\r\n        <BingoBoard\r\n          boardData={board.board_data}\r\n          progressData={progress}\r\n          isReadOnly={!isOwnBoard}\r\n          userId={boardOwner.id}\r\n          boardId={board.id}\r\n        />\r\n      )}\r\n\r\n      <Leaderboard />\r\n\r\n      <button onClick={handleBackToDashboard} className=\"back-button\">\r\n        Back to Dashboard\r\n      </button>\r\n\r\n      {/* How to Play and Rules sections */}\r\n      <div className=\"dashboard-info-sections\">\r\n        <Rules />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BoardViewer;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,SAASC,OAAO,QAAQ,qBAAqB;;AAE7C;AACA,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,KAAK,QAAQ,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB;EACA,MAAM;IAAEC;EAAY,CAAC,GAAGV,SAAS,CAAC,CAAC;EACnC,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM;IAAEW;EAAK,CAAC,GAAGV,OAAO,CAAC,CAAC;;EAE1B;EACA,MAAM,CAACW,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMyB,UAAU,GAAGX,IAAI,IAAIF,WAAW,CAACc,WAAW,CAAC,CAAC,KAAKZ,IAAI,CAACa,YAAY,CAACD,WAAW,CAAC,CAAC;;EAExF;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM2B,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI;QACFN,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMO,aAAa,GAAG,MAAMC,KAAK,CAAC,oCAAoCC,kBAAkB,CAACnB,WAAW,CAAC,EAAE,CAAC;QAExG,IAAI,CAACiB,aAAa,CAACG,EAAE,EAAE;UACrB,IAAIH,aAAa,CAACI,MAAM,KAAK,GAAG,EAAE;YAChC,MAAM,IAAIC,KAAK,CAAC,iDAAiD,CAAC;UACpE;UACA,MAAM,IAAIA,KAAK,CAAC,uBAAuBL,aAAa,CAACI,MAAM,EAAE,CAAC;QAChE;QAEA,MAAME,SAAS,GAAG,MAAMN,aAAa,CAACO,IAAI,CAAC,CAAC;QAC5ClB,QAAQ,CAACiB,SAAS,CAAClB,KAAK,CAAC;QACzBD,aAAa,CAACmB,SAAS,CAACrB,IAAI,CAAC;;QAE7B;QACA,MAAMuB,gBAAgB,GAAG,MAAMP,KAAK,CAAC,oCAAoCC,kBAAkB,CAACnB,WAAW,CAAC,WAAW,CAAC;QAEpH,IAAI,CAACyB,gBAAgB,CAACL,EAAE,EAAE;UACxB,MAAM,IAAIE,KAAK,CAAC,uBAAuBG,gBAAgB,CAACJ,MAAM,EAAE,CAAC;QACnE;QAEA,MAAMK,YAAY,GAAG,MAAMD,gBAAgB,CAACD,IAAI,CAAC,CAAC;QAClDhB,WAAW,CAACkB,YAAY,CAAC;MAE3B,CAAC,CAAC,OAAOC,GAAG,EAAE;QACZC,OAAO,CAACjB,KAAK,CAAC,4BAA4B,EAAEgB,GAAG,CAAC;QAChDf,QAAQ,CAACe,GAAG,CAACE,OAAO,IAAI,oDAAoD,CAAC;MAC/E,CAAC,SAAS;QACRnB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDM,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAChB,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM8B,qBAAqB,GAAGA,CAAA,KAAM;IAClC7B,QAAQ,CAAC,YAAY,CAAC;EACxB,CAAC;;EAED;EACA,IAAIQ,OAAO,EAAE;IACX,oBAAOZ,OAAA;MAAKkC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAAC;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAChE;;EAEA;EACA,IAAIzB,KAAK,EAAE;IACT,oBACEd,OAAA;MAAKkC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BnC,OAAA;QAAKkC,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAErB;MAAK;QAAAsB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5CvC,OAAA;QAAQwC,OAAO,EAAEP,qBAAsB;QAACC,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAEhE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACEvC,OAAA;IAAKkC,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBACrCnC,OAAA;MAAKkC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClCnC,OAAA;QAAIkC,SAAS,EAAE,eAAelB,UAAU,GAAG,kBAAkB,GAAG,EAAE,EAAG;QAAAmB,QAAA,EAClEnB,UAAU,GAAG,YAAY,GAAG,GAAGV,UAAU,CAACY,YAAY;MAAU;QAAAkB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEL/B,KAAK,IAAIE,QAAQ,IAAIJ,UAAU,iBAC9BN,OAAA,CAACJ,UAAU;MACT8B,SAAS,EAAElB,KAAK,CAACiC,UAAW;MAC5BZ,YAAY,EAAEnB,QAAS;MACvBgC,UAAU,EAAE,CAAC1B,UAAW;MACxB2B,MAAM,EAAErC,UAAU,CAACsC,EAAG;MACtBC,OAAO,EAAErC,KAAK,CAACoC;IAAG;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CACF,eAEDvC,OAAA,CAACH,WAAW;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEfvC,OAAA;MAAQwC,OAAO,EAAEP,qBAAsB;MAACC,SAAS,EAAC,aAAa;MAAAC,QAAA,EAAC;IAEhE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAGTvC,OAAA;MAAKkC,SAAS,EAAC,yBAAyB;MAAAC,QAAA,eACtCnC,OAAA,CAACF,KAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrC,EAAA,CA/GID,WAAW;EAAA,QAESR,SAAS,EAChBC,WAAW,EAGXC,OAAO;AAAA;AAAAmD,EAAA,GANpB7C,WAAW;AAiHjB,eAAeA,WAAW;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}