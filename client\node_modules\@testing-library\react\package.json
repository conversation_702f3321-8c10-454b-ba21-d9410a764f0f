{"_from": "@testing-library/react@^16.3.0", "_id": "@testing-library/react@16.3.0", "_inBundle": false, "_integrity": "sha512-kFSyxiEDwv1WLl2fgsq6pPBbw5aWKrsY2/noi1Id0TK0UParSF62oFQFGHXIyaG4pp2tEub/Zlel+fjjZILDsw==", "_location": "/@testing-library/react", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@testing-library/react@^16.3.0", "name": "@testing-library/react", "escapedName": "@testing-library%2freact", "scope": "@testing-library", "rawSpec": "^16.3.0", "saveSpec": null, "fetchSpec": "^16.3.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@testing-library/react/-/react-16.3.0.tgz", "_shasum": "3a85bb9bdebf180cd76dba16454e242564d598a6", "_spec": "@testing-library/react@^16.3.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client", "author": {"name": "Kent <PERSON>", "email": "<EMAIL>", "url": "https://kentcdodds.com"}, "bugs": {"url": "https://github.com/testing-library/react-testing-library/issues"}, "bundleDependencies": false, "dependencies": {"@babel/runtime": "^7.12.5"}, "deprecated": false, "description": "Simple and complete React DOM testing utilities that encourage good testing practices.", "devDependencies": {"@testing-library/dom": "^10.0.0", "@testing-library/jest-dom": "^5.11.6", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "chalk": "^4.1.2", "dotenv-cli": "^4.0.0", "jest-diff": "^29.7.0", "kcd-scripts": "^13.0.0", "npm-run-all": "^4.1.5", "react": "^19.0.0", "react-dom": "^19.0.0", "rimraf": "^3.0.2", "typescript": "^4.1.2"}, "engines": {"node": ">=18"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js", "parserOptions": {"ecmaVersion": 2022}, "globals": {"globalThis": "readonly"}, "rules": {"react/prop-types": "off", "react/no-adjacent-inline-elements": "off", "import/no-unassigned-import": "off", "import/named": "off", "testing-library/no-container": "off", "testing-library/no-debugging-utils": "off", "testing-library/no-dom-import": "off", "testing-library/no-unnecessary-act": "off", "testing-library/prefer-explicit-assert": "off", "testing-library/prefer-find-by": "off", "testing-library/prefer-user-event": "off"}}, "eslintIgnore": ["node_modules", "coverage", "dist", "*.d.ts"], "files": ["dist", "dont-cleanup-after-each.js", "pure.js", "pure.d.ts", "types/*.d.ts"], "homepage": "https://github.com/testing-library/react-testing-library#readme", "keywords": ["testing", "react", "ui", "dom", "jsdom", "unit", "integration", "functional", "end-to-end", "e2e"], "license": "MIT", "main": "dist/index.js", "module": "dist/@testing-library/react.esm.js", "name": "@testing-library/react", "peerDependencies": {"@testing-library/dom": "^10.0.0", "@types/react": "^18.0.0 || ^19.0.0", "@types/react-dom": "^18.0.0 || ^19.0.0", "react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}, "repository": {"type": "git", "url": "git+https://github.com/testing-library/react-testing-library.git"}, "scripts": {"build": "npm-run-all --parallel build:main build:bundle:main build:bundle:pure", "build:bundle:main": "dotenv -e .bundle.main.env kcd-scripts build -- --bundle --no-clean", "build:bundle:pure": "dotenv -e .bundle.main.env -e .bundle.pure.env kcd-scripts build -- --bundle --no-clean", "build:main": "kcd-scripts build --no-clean", "format": "kcd-scripts format", "install:csb": "npm install", "lint": "kcd-scripts lint", "prebuild": "<PERSON><PERSON><PERSON> dist", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:update": "npm test -- --updateSnapshot --coverage", "typecheck": "kcd-scripts typecheck --build types", "validate": "kcd-scripts validate"}, "types": "types/index.d.ts", "version": "16.3.0"}