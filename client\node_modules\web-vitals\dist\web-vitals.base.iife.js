!function(e){"use strict";var t=function(e,t){return{name:e,value:void 0===t?-1:t,delta:0,entries:[],id:"v2-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12)}},n=function(e,t){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){if("first-input"===e&&!("PerformanceEventTiming"in self))return;var n=new PerformanceObserver((function(e){return e.getEntries().map(t)}));return n.observe({type:e,buffered:!0}),n}}catch(e){}},i=function(e,t){var n=function n(i){"pagehide"!==i.type&&"hidden"!==document.visibilityState||(e(i),t&&(removeEventListener("visibilitychange",n,!0),removeEventListener("pagehide",n,!0)))};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},r=function(e){addEventListener("pageshow",(function(t){t.persisted&&e(t)}),!0)},a=function(e,t,n){var i;return function(r){t.value>=0&&(r||n)&&(t.delta=t.value-(i||0),(t.delta||void 0===i)&&(i=t.value,e(t)))}},o=-1,u=function(){i((function(e){var t=e.timeStamp;o=t}),!0)},c=function(){return o<0&&((o=window.webVitals.firstHiddenTime)===1/0&&u(),r((function(){setTimeout((function(){o="hidden"===document.visibilityState?0:1/0,u()}),0)}))),{get firstHiddenTime(){return o}}},s=function(e,i){var o,u=c(),s=t("FCP"),f=function(e){"first-contentful-paint"===e.name&&(m&&m.disconnect(),e.startTime<u.firstHiddenTime&&(s.value=e.startTime,s.entries.push(e),o(!0)))},d=window.performance&&performance.getEntriesByName&&performance.getEntriesByName("first-contentful-paint")[0],m=d?null:n("paint",f);(d||m)&&(o=a(e,s,i),d&&f(d),r((function(n){s=t("FCP"),o=a(e,s,i),requestAnimationFrame((function(){requestAnimationFrame((function(){s.value=performance.now()-n.timeStamp,o(!0)}))}))})))},f=!1,d=-1,m={};e.getCLS=function(e,o){f||(s((function(e){d=e.value})),f=!0);var u,c=function(t){d>-1&&e(t)},m=t("CLS",0),v=0,l=[],p=function(e){if(!e.hadRecentInput){var t=l[0],n=l[l.length-1];v&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(v+=e.value,l.push(e)):(v=e.value,l=[e]),v>m.value&&(m.value=v,m.entries=l,u())}},T=n("layout-shift",p);T&&(u=a(c,m,o),i((function(){T.takeRecords().map(p),u(!0)})),r((function(){v=0,d=-1,m=t("CLS",0),u=a(c,m,o)})))},e.getFCP=s,e.getFID=function(e,o){var u,s=c(),f=t("FID"),d=function(e){e.startTime<s.firstHiddenTime&&(f.value=e.processingStart-e.startTime,f.entries.push(e),u(!0))},m=n("first-input",d);u=a(e,f,o),m&&i((function(){m.takeRecords().map(d),m.disconnect()}),!0),m||window.webVitals.firstInputPolyfill(d),r((function(){f=t("FID"),u=a(e,f,o),window.webVitals.resetFirstInputPolyfill(),window.webVitals.firstInputPolyfill(d)}))},e.getLCP=function(e,o){var u,s=c(),f=t("LCP"),d=function(e){var t=e.startTime;t<s.firstHiddenTime&&(f.value=t,f.entries.push(e),u())},v=n("largest-contentful-paint",d);if(v){u=a(e,f,o);var l=function(){m[f.id]||(v.takeRecords().map(d),v.disconnect(),m[f.id]=!0,u(!0))};["keydown","click"].forEach((function(e){addEventListener(e,l,{once:!0,capture:!0})})),i(l,!0),r((function(n){f=t("LCP"),u=a(e,f,o),requestAnimationFrame((function(){requestAnimationFrame((function(){f.value=performance.now()-n.timeStamp,m[f.id]=!0,u(!0)}))}))}))}},e.getTTFB=function(e){var n,i=t("TTFB");n=function(){try{var t=performance.getEntriesByType("navigation")[0]||function(){var e=performance.timing,t={entryType:"navigation",startTime:0};for(var n in e)"navigationStart"!==n&&"toJSON"!==n&&(t[n]=Math.max(e[n]-e.navigationStart,0));return t}();if(i.value=i.delta=t.responseStart,i.value<0||i.value>performance.now())return;i.entries=[t],e(i)}catch(e){}},"complete"===document.readyState?setTimeout(n,0):addEventListener("load",(function(){return setTimeout(n,0)}))},Object.defineProperty(e,"__esModule",{value:!0})}(this.webVitals=this.webVitals||{});
