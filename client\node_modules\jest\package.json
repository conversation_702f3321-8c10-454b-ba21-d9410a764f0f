{"_from": "jest@^27.4.3", "_id": "jest@27.5.1", "_inBundle": false, "_integrity": "sha512-Yn0mADZB89zTtjkPJEXwrac3LHudkQMR+Paqa8uxJHCBr9agxztUifWCyiYrjhMPBoUVBjyny0I7XH6ozDr7QQ==", "_location": "/jest", "_phantomChildren": {"@jest/core": "27.5.1", "@jest/test-result": "27.5.1", "@jest/types": "27.5.1", "chalk": "4.1.2", "exit": "0.1.2", "graceful-fs": "4.2.11", "import-local": "3.2.0", "jest-config": "27.5.1", "jest-util": "27.5.1", "jest-validate": "27.5.1", "prompts": "2.4.2", "yargs": "16.2.0"}, "_requested": {"type": "range", "registry": true, "raw": "jest@^27.4.3", "name": "jest", "escapedName": "jest", "rawSpec": "^27.4.3", "saveSpec": null, "fetchSpec": "^27.4.3"}, "_requiredBy": ["/react-scripts"], "_resolved": "https://registry.npmjs.org/jest/-/jest-27.5.1.tgz", "_shasum": "dadf33ba70a779be7a6fc33015843b51494f63fc", "_spec": "jest@^27.4.3", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\node_modules\\react-scripts", "bin": {"jest": "bin/jest.js"}, "bugs": {"url": "https://github.com/facebook/jest/issues"}, "bundleDependencies": false, "dependencies": {"@jest/core": "^27.5.1", "import-local": "^3.0.2", "jest-cli": "^27.5.1"}, "deprecated": false, "description": "Delightful JavaScript Testing.", "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "exports": {".": {"types": "./build/jest.d.ts", "default": "./build/jest.js"}, "./package.json": "./package.json", "./bin/jest": "./bin/jest.js"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850", "homepage": "https://jestjs.io/", "keywords": ["ava", "babel", "coverage", "easy", "expect", "facebook", "immersive", "instant", "jasmine", "jest", "jsdom", "mocha", "mocking", "painless", "qunit", "runner", "sandboxed", "snapshot", "tap", "tape", "test", "testing", "typescript", "watch"], "license": "MIT", "main": "./build/jest.js", "name": "jest", "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/facebook/jest.git"}, "types": "./build/jest.d.ts", "version": "27.5.1"}