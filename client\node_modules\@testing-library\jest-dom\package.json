{"_from": "@testing-library/jest-dom@^6.6.3", "_id": "@testing-library/jest-dom@6.6.3", "_inBundle": false, "_integrity": "sha512-IteBhl4XqYNkM54f4ejhLRJiZNqcSCoXUOG2CPK7qbD322KjQozM4kHQOfkG2oln9b9HTYqs+Sae8vBATubxxA==", "_location": "/@testing-library/jest-dom", "_phantomChildren": {"ansi-styles": "4.3.0", "supports-color": "7.2.0"}, "_requested": {"type": "range", "registry": true, "raw": "@testing-library/jest-dom@^6.6.3", "name": "@testing-library/jest-dom", "escapedName": "@testing-library%2fjest-dom", "scope": "@testing-library", "rawSpec": "^6.6.3", "saveSpec": null, "fetchSpec": "^6.6.3"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@testing-library/jest-dom/-/jest-dom-6.6.3.tgz", "_shasum": "26ba906cf928c0f8172e182c6fe214eb4f9f2bd2", "_spec": "@testing-library/jest-dom@^6.6.3", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://gnapse.github.io"}, "bugs": {"url": "https://github.com/testing-library/jest-dom/issues"}, "bundleDependencies": false, "dependencies": {"@adobe/css-tools": "^4.4.0", "aria-query": "^5.0.0", "chalk": "^3.0.0", "css.escape": "^1.5.1", "dom-accessibility-api": "^0.6.3", "lodash": "^4.17.21", "redent": "^3.0.0"}, "deprecated": false, "description": "Custom jest matchers to test the state of the DOM", "devDependencies": {"@jest/globals": "^29.6.2", "@rollup/plugin-commonjs": "^25.0.4", "@types/bun": "latest", "@types/web": "latest", "expect": "^29.6.2", "jest-environment-jsdom-sixteen": "^1.0.3", "jest-watch-select-projects": "^2.0.0", "jsdom": "^16.2.1", "kcd-scripts": "^14.0.0", "pretty-format": "^25.1.0", "rollup": "^3.28.1", "rollup-plugin-delete": "^2.0.0", "typescript": "^5.1.6", "vitest": "^0.34.1"}, "engines": {"node": ">=14", "npm": ">=6", "yarn": ">=1"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js", "parserOptions": {"sourceType": "module", "ecmaVersion": 2020}, "rules": {"no-invalid-this": "off"}, "overrides": [{"files": ["src/__tests__/*.js"], "rules": {"max-lines-per-function": "off"}}, {"files": ["**/*.d.ts"], "rules": {"@typescript-eslint/no-empty-interface": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-invalid-void-type": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/triple-slash-reference": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist", "types/__tests__"], "exports": {".": {"require": {"types": "./types/index.d.ts", "default": "./dist/index.js"}, "import": {"types": "./types/index.d.ts", "default": "./dist/index.mjs"}}, "./jest-globals": {"require": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.js"}, "import": {"types": "./types/jest-globals.d.ts", "default": "./dist/jest-globals.mjs"}}, "./matchers": {"require": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.js"}, "import": {"types": "./types/matchers-standalone.d.ts", "default": "./dist/matchers.mjs"}}, "./vitest": {"require": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.js"}, "import": {"types": "./types/vitest.d.ts", "default": "./dist/vitest.mjs"}}, "./package.json": "./package.json"}, "files": ["dist", "types", "*.d.ts", "jest-globals.js", "matchers.js", "vitest.js"], "homepage": "https://github.com/testing-library/jest-dom#readme", "keywords": ["testing", "dom", "jest", "jsdom"], "license": "MIT", "main": "dist/index.js", "module": "dist/index.mjs", "name": "@testing-library/jest-dom", "repository": {"type": "git", "url": "git+https://github.com/testing-library/jest-dom.git"}, "scripts": {"build": "rollup -c", "format": "kcd-scripts format", "lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:types": "tsc -p types/__tests__/jest && tsc -p types/__tests__/jest-globals && tsc -p types/__tests__/vitest && tsc -p types/__tests__/bun", "test:update": "npm test -- --updateSnapshot --coverage", "validate": "kcd-scripts validate && npm run test:types"}, "types": "types/index.d.ts", "version": "6.6.3"}