{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Auth\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useUser } from './UserContext';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  // State for form inputs\n  const [pin, setPin] = useState('');\n  const [displayName, setDisplayName] = useState('');\n  const [isRegistering, setIsRegistering] = useState(false);\n  const [formError, setFormError] = useState('');\n\n  // Get user context and navigation\n  const {\n    login,\n    register,\n    loading\n  } = useUser();\n  const navigate = useNavigate();\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setFormError('');\n    if (isRegistering) {\n      // Validate registration inputs\n      if (!pin || !displayName) {\n        setFormError('Please enter both PIN and display name');\n        return;\n      }\n\n      // Register new user\n      const result = await register(pin, displayName);\n      if (result.success) {\n        // Redirect to dashboard on success (new users won't have boards yet)\n        navigate('/dashboard');\n      } else {\n        setFormError(result.error);\n      }\n    } else {\n      // Validate login input\n      if (!pin) {\n        setFormError('Please enter your PIN');\n        return;\n      }\n\n      // Login existing user\n      const result = await login(pin);\n      if (result.success) {\n        // Check if user has a board and redirect accordingly\n        try {\n          var _result$user;\n          const boardResponse = await fetch(`http://localhost:5000/api/users/${((_result$user = result.user) === null || _result$user === void 0 ? void 0 : _result$user.id) || JSON.parse(localStorage.getItem('user')).id}/board`, {\n            method: 'GET',\n            headers: {\n              'Content-Type': 'application/json'\n            }\n          });\n          if (boardResponse.ok) {\n            // User has a board, redirect to their board\n            const userData = JSON.parse(localStorage.getItem('user'));\n            navigate(`/boards/${encodeURIComponent(userData.display_name)}`);\n          } else {\n            // User doesn't have a board, redirect to dashboard\n            navigate('/dashboard');\n          }\n        } catch (err) {\n          console.error('Error checking user board:', err);\n          // Fallback to dashboard\n          navigate('/dashboard');\n        }\n      } else {\n        setFormError(result.error);\n      }\n    }\n  };\n\n  // Toggle between login and registration forms\n  const toggleForm = () => {\n    setIsRegistering(!isRegistering);\n    setFormError('');\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auth-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-form-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: isRegistering ? 'Create Account' : 'Login'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this), formError && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-error\",\n        children: formError\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 23\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"auth-form\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"pin\",\n            children: \"PIN\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"pin\",\n            value: pin,\n            onChange: e => setPin(e.target.value),\n            placeholder: \"Enter your PIN\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), isRegistering && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"displayName\",\n            children: \"Display Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 104,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            id: \"displayName\",\n            value: displayName,\n            onChange: e => setDisplayName(e.target.value),\n            placeholder: \"Enter your display name\",\n            disabled: loading\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"auth-button\",\n          disabled: loading,\n          children: loading ? 'Processing...' : isRegistering ? 'Register' : 'Login'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-toggle\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: toggleForm,\n          className: \"toggle-button\",\n          disabled: loading,\n          children: isRegistering ? 'Already have an account? Login' : 'New user? Create account'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"Dm52Gihjn0mD2RqCoyoPTKldPcY=\", false, function () {\n  return [useUser, useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useUser", "useNavigate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "pin", "setPin", "displayName", "setDisplayName", "isRegistering", "setIsRegistering", "formError", "setFormError", "login", "register", "loading", "navigate", "handleSubmit", "e", "preventDefault", "result", "success", "error", "_result$user", "boardResponse", "fetch", "user", "id", "JSON", "parse", "localStorage", "getItem", "method", "headers", "ok", "userData", "encodeURIComponent", "display_name", "err", "console", "toggleForm", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "value", "onChange", "target", "placeholder", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Auth/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { useUser } from './UserContext';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst Login = () => {\r\n  // State for form inputs\r\n  const [pin, setPin] = useState('');\r\n  const [displayName, setDisplayName] = useState('');\r\n  const [isRegistering, setIsRegistering] = useState(false);\r\n  const [formError, setFormError] = useState('');\r\n\r\n  // Get user context and navigation\r\n  const { login, register, loading } = useUser();\r\n  const navigate = useNavigate();\r\n\r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setFormError('');\r\n\r\n    if (isRegistering) {\r\n      // Validate registration inputs\r\n      if (!pin || !displayName) {\r\n        setFormError('Please enter both PIN and display name');\r\n        return;\r\n      }\r\n\r\n      // Register new user\r\n      const result = await register(pin, displayName);\r\n\r\n      if (result.success) {\r\n        // Redirect to dashboard on success (new users won't have boards yet)\r\n        navigate('/dashboard');\r\n      } else {\r\n        setFormError(result.error);\r\n      }\r\n    } else {\r\n      // Validate login input\r\n      if (!pin) {\r\n        setFormError('Please enter your PIN');\r\n        return;\r\n      }\r\n\r\n      // Login existing user\r\n      const result = await login(pin);\r\n\r\n      if (result.success) {\r\n        // Check if user has a board and redirect accordingly\r\n        try {\r\n          const boardResponse = await fetch(`http://localhost:5000/api/users/${result.user?.id || JSON.parse(localStorage.getItem('user')).id}/board`, {\r\n            method: 'GET',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n          });\r\n\r\n          if (boardResponse.ok) {\r\n            // User has a board, redirect to their board\r\n            const userData = JSON.parse(localStorage.getItem('user'));\r\n            navigate(`/boards/${encodeURIComponent(userData.display_name)}`);\r\n          } else {\r\n            // User doesn't have a board, redirect to dashboard\r\n            navigate('/dashboard');\r\n          }\r\n        } catch (err) {\r\n          console.error('Error checking user board:', err);\r\n          // Fallback to dashboard\r\n          navigate('/dashboard');\r\n        }\r\n      } else {\r\n        setFormError(result.error);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Toggle between login and registration forms\r\n  const toggleForm = () => {\r\n    setIsRegistering(!isRegistering);\r\n    setFormError('');\r\n  };\r\n\r\n  return (\r\n    <div className=\"auth-container\">\r\n      <div className=\"auth-form-container\">\r\n        <h2>{isRegistering ? 'Create Account' : 'Login'}</h2>\r\n\r\n        {formError && <div className=\"auth-error\">{formError}</div>}\r\n\r\n        <form onSubmit={handleSubmit} className=\"auth-form\">\r\n          <div className=\"form-group\">\r\n            <label htmlFor=\"pin\">PIN</label>\r\n            <input\r\n              type=\"text\"\r\n              id=\"pin\"\r\n              value={pin}\r\n              onChange={(e) => setPin(e.target.value)}\r\n              placeholder=\"Enter your PIN\"\r\n              disabled={loading}\r\n            />\r\n          </div>\r\n\r\n          {isRegistering && (\r\n            <div className=\"form-group\">\r\n              <label htmlFor=\"displayName\">Display Name</label>\r\n              <input\r\n                type=\"text\"\r\n                id=\"displayName\"\r\n                value={displayName}\r\n                onChange={(e) => setDisplayName(e.target.value)}\r\n                placeholder=\"Enter your display name\"\r\n                disabled={loading}\r\n              />\r\n            </div>\r\n          )}\r\n\r\n          <button type=\"submit\" className=\"auth-button\" disabled={loading}>\r\n            {loading ? 'Processing...' : isRegistering ? 'Register' : 'Login'}\r\n          </button>\r\n        </form>\r\n\r\n        <div className=\"auth-toggle\">\r\n          <button onClick={toggleForm} className=\"toggle-button\" disabled={loading}>\r\n            {isRegistering ? 'Already have an account? Login' : 'New user? Create account'}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,OAAO,QAAQ,eAAe;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB;EACA,MAAM,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAClC,MAAM,CAACS,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACW,aAAa,EAAEC,gBAAgB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACa,SAAS,EAAEC,YAAY,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;;EAE9C;EACA,MAAM;IAAEe,KAAK;IAAEC,QAAQ;IAAEC;EAAQ,CAAC,GAAGhB,OAAO,CAAC,CAAC;EAC9C,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMiB,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBP,YAAY,CAAC,EAAE,CAAC;IAEhB,IAAIH,aAAa,EAAE;MACjB;MACA,IAAI,CAACJ,GAAG,IAAI,CAACE,WAAW,EAAE;QACxBK,YAAY,CAAC,wCAAwC,CAAC;QACtD;MACF;;MAEA;MACA,MAAMQ,MAAM,GAAG,MAAMN,QAAQ,CAACT,GAAG,EAAEE,WAAW,CAAC;MAE/C,IAAIa,MAAM,CAACC,OAAO,EAAE;QAClB;QACAL,QAAQ,CAAC,YAAY,CAAC;MACxB,CAAC,MAAM;QACLJ,YAAY,CAACQ,MAAM,CAACE,KAAK,CAAC;MAC5B;IACF,CAAC,MAAM;MACL;MACA,IAAI,CAACjB,GAAG,EAAE;QACRO,YAAY,CAAC,uBAAuB,CAAC;QACrC;MACF;;MAEA;MACA,MAAMQ,MAAM,GAAG,MAAMP,KAAK,CAACR,GAAG,CAAC;MAE/B,IAAIe,MAAM,CAACC,OAAO,EAAE;QAClB;QACA,IAAI;UAAA,IAAAE,YAAA;UACF,MAAMC,aAAa,GAAG,MAAMC,KAAK,CAAC,mCAAmC,EAAAF,YAAA,GAAAH,MAAM,CAACM,IAAI,cAAAH,YAAA,uBAAXA,YAAA,CAAaI,EAAE,KAAIC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,CAACJ,EAAE,QAAQ,EAAE;YAC3IK,MAAM,EAAE,KAAK;YACbC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB;UACF,CAAC,CAAC;UAEF,IAAIT,aAAa,CAACU,EAAE,EAAE;YACpB;YACA,MAAMC,QAAQ,GAAGP,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;YACzDf,QAAQ,CAAC,WAAWoB,kBAAkB,CAACD,QAAQ,CAACE,YAAY,CAAC,EAAE,CAAC;UAClE,CAAC,MAAM;YACL;YACArB,QAAQ,CAAC,YAAY,CAAC;UACxB;QACF,CAAC,CAAC,OAAOsB,GAAG,EAAE;UACZC,OAAO,CAACjB,KAAK,CAAC,4BAA4B,EAAEgB,GAAG,CAAC;UAChD;UACAtB,QAAQ,CAAC,YAAY,CAAC;QACxB;MACF,CAAC,MAAM;QACLJ,YAAY,CAACQ,MAAM,CAACE,KAAK,CAAC;MAC5B;IACF;EACF,CAAC;;EAED;EACA,MAAMkB,UAAU,GAAGA,CAAA,KAAM;IACvB9B,gBAAgB,CAAC,CAACD,aAAa,CAAC;IAChCG,YAAY,CAAC,EAAE,CAAC;EAClB,CAAC;EAED,oBACEV,OAAA;IAAKuC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC7BxC,OAAA;MAAKuC,SAAS,EAAC,qBAAqB;MAAAC,QAAA,gBAClCxC,OAAA;QAAAwC,QAAA,EAAKjC,aAAa,GAAG,gBAAgB,GAAG;MAAO;QAAAkC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EAEpDnC,SAAS,iBAAIT,OAAA;QAAKuC,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAE/B;MAAS;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAE3D5C,OAAA;QAAM6C,QAAQ,EAAE9B,YAAa;QAACwB,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjDxC,OAAA;UAAKuC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxC,OAAA;YAAO8C,OAAO,EAAC,KAAK;YAAAN,QAAA,EAAC;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAChC5C,OAAA;YACE+C,IAAI,EAAC,MAAM;YACXtB,EAAE,EAAC,KAAK;YACRuB,KAAK,EAAE7C,GAAI;YACX8C,QAAQ,EAAGjC,CAAC,IAAKZ,MAAM,CAACY,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;YACxCG,WAAW,EAAC,gBAAgB;YAC5BC,QAAQ,EAAEvC;UAAQ;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,EAELrC,aAAa,iBACZP,OAAA;UAAKuC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBxC,OAAA;YAAO8C,OAAO,EAAC,aAAa;YAAAN,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACjD5C,OAAA;YACE+C,IAAI,EAAC,MAAM;YACXtB,EAAE,EAAC,aAAa;YAChBuB,KAAK,EAAE3C,WAAY;YACnB4C,QAAQ,EAAGjC,CAAC,IAAKV,cAAc,CAACU,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;YAChDG,WAAW,EAAC,yBAAyB;YACrCC,QAAQ,EAAEvC;UAAQ;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAED5C,OAAA;UAAQ+C,IAAI,EAAC,QAAQ;UAACR,SAAS,EAAC,aAAa;UAACa,QAAQ,EAAEvC,OAAQ;UAAA2B,QAAA,EAC7D3B,OAAO,GAAG,eAAe,GAAGN,aAAa,GAAG,UAAU,GAAG;QAAO;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEP5C,OAAA;QAAKuC,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BxC,OAAA;UAAQqD,OAAO,EAAEf,UAAW;UAACC,SAAS,EAAC,eAAe;UAACa,QAAQ,EAAEvC,OAAQ;UAAA2B,QAAA,EACtEjC,aAAa,GAAG,gCAAgC,GAAG;QAA0B;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CA5HID,KAAK;EAAA,QAQ4BJ,OAAO,EAC3BC,WAAW;AAAA;AAAAwD,EAAA,GATxBrD,KAAK;AA8HX,eAAeA,KAAK;AAAC,IAAAqD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}