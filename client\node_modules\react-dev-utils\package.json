{"_from": "react-dev-utils@^12.0.1", "_id": "react-dev-utils@12.0.1", "_inBundle": false, "_integrity": "sha512-84Ivxmr17KjUupyqzFode6xKhjwuEJDROWKJy/BthkL7Wn6NJ8h4WE6k/exAv6ImS+0oZLRRW5j/aINMHyeGeQ==", "_location": "/react-dev-utils", "_phantomChildren": {"path-exists": "4.0.0", "yocto-queue": "0.1.0"}, "_requested": {"type": "range", "registry": true, "raw": "react-dev-utils@^12.0.1", "name": "react-dev-utils", "escapedName": "react-dev-utils", "rawSpec": "^12.0.1", "saveSpec": null, "fetchSpec": "^12.0.1"}, "_requiredBy": ["/react-scripts"], "_resolved": "https://registry.npmjs.org/react-dev-utils/-/react-dev-utils-12.0.1.tgz", "_shasum": "ba92edb4a1f379bd46ccd6bcd4e7bc398df33e73", "_spec": "react-dev-utils@^12.0.1", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\node_modules\\react-scripts", "bugs": {"url": "https://github.com/facebook/create-react-app/issues"}, "bundleDependencies": false, "dependencies": {"@babel/code-frame": "^7.16.0", "address": "^1.1.2", "browserslist": "^4.18.1", "chalk": "^4.1.2", "cross-spawn": "^7.0.3", "detect-port-alt": "^1.1.6", "escape-string-regexp": "^4.0.0", "filesize": "^8.0.6", "find-up": "^5.0.0", "fork-ts-checker-webpack-plugin": "^6.5.0", "global-modules": "^2.0.0", "globby": "^11.0.4", "gzip-size": "^6.0.0", "immer": "^9.0.7", "is-root": "^2.1.0", "loader-utils": "^3.2.0", "open": "^8.4.0", "pkg-up": "^3.1.0", "prompts": "^2.4.2", "react-error-overlay": "^6.0.11", "recursive-readdir": "^2.2.2", "shell-quote": "^1.7.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "deprecated": false, "description": "webpack utilities used by Create React App", "devDependencies": {"cross-env": "^7.0.3", "jest": "^27.4.3"}, "engines": {"node": ">=14"}, "files": ["browsersHelper.js", "chalk.js", "checkRequiredFiles.js", "clearConsole.js", "crossSpawn.js", "errorOverlayMiddleware.js", "eslintFormatter.js", "evalSourceMapMiddleware.js", "FileSizeReporter.js", "ForkTsCheckerWebpackPlugin.js", "ForkTsCheckerWarningWebpackPlugin.js", "formatWebpackMessages.js", "getCacheIdentifier.js", "getCSSModuleLocalIdent.js", "getProcessForPort.js", "getPublicUrlOrPath.js", "globby.js", "ignoredFiles.js", "immer.js", "InlineChunkHtmlPlugin.js", "InterpolateHtmlPlugin.js", "launchEditor.js", "launchEditorEndpoint.js", "ModuleNotFoundPlugin.js", "ModuleScopePlugin.js", "noopServiceWorkerMiddleware.js", "openBrowser.js", "openChrome.applescript", "printBuildError.js", "printHostingInstructions.js", "redirectServedPathMiddleware.js", "refreshOverlayInterop.js", "typescriptFormatter.js", "WebpackDevServerUtils.js", "webpackHotDevClient.js"], "gitHead": "19fa58d527ae74f2b6baa0867463eea1d290f9a5", "homepage": "https://github.com/facebook/create-react-app#readme", "license": "MIT", "name": "react-dev-utils", "repository": {"type": "git", "url": "git+https://github.com/facebook/create-react-app.git", "directory": "packages/react-dev-utils"}, "scripts": {"test": "cross-env FORCE_COLOR=true jest"}, "version": "12.0.1"}