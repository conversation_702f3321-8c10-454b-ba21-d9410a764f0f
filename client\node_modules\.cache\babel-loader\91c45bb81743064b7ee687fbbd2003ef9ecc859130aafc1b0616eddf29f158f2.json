{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\App.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation, Link } from 'react-router-dom';\nimport './App.css';\n\n// Import components\nimport { UserProvider, useUser } from './components/Auth/UserContext';\nimport Login from './components/Auth/Login';\nimport UserDashboard from './components/Dashboard/UserDashboard';\nimport BoardViewer from './components/Board/BoardViewer';\nimport Leaderboard from './components/Board/Leaderboard';\nimport Cards from './components/Cards/Cards';\nimport HowToPlay from './components/HowToPlay/HowToPlay';\n\n// Protected Route component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    user,\n    loading\n  } = useUser();\n\n  // If still loading, show loading indicator\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"loading-message\",\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 12\n    }, this);\n  }\n\n  // If not logged in, redirect to login page\n  if (!user) {\n    return /*#__PURE__*/_jsxDEV(Navigate, {\n      to: \"/\",\n      replace: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 12\n    }, this);\n  }\n\n  // If logged in, render the protected component\n  return children;\n};\n\n// Rules component\n_s(ProtectedRoute, \"ZdzcWmnLKUUPL49SmQkyBG2sy0w=\", false, function () {\n  return [useUser];\n});\n_c = ProtectedRoute;\nexport const Rules = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"rules-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      id: \"rules\",\n      className: \"rules-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Rules\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Please follow these important guidelines for a fun and respectful experience:\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ol\", {\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Cosplay is NOT consent - always ask for permission before taking photos.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"You must be featured in the photo (at least a hand) to claim a square.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Teams can have as many members as you'd like, but there will still only be a single prize per team.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"You can refresh to get a new board, but you will lose all your current progress.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Each player receives a unique bingo board with characters of different rarities.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Characters are ranked by rarity: FREE, R, SR, SSR, and UR+.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"Higher rarity characters are worth more points, but are harder to find.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Good luck, and happy hunting!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 5\n  }, this);\n};\n\n// Header component with logo and thank you message in same row\n_c2 = Rules;\nconst AppHeader = () => {\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"app-header\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"header-row\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-container\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          src: `${process.env.PUBLIC_URL}/title-logo.png`,\n          alt: \"Bimbo Hunter Logo\",\n          className: \"title-logo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 59,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"thank-you-message\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Thank you for competing in Official 2025 Bimbo Hunt!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this);\n};\n\n// Navigation component\n_c3 = AppHeader;\nconst Navigation = () => {\n  _s2();\n  const location = useLocation();\n  const {\n    user\n  } = useUser();\n\n  // Only show navigation for logged-in users\n  if (!user) return null;\n  const navItems = [{\n    path: '/dashboard',\n    label: 'Your Board'\n  }, {\n    path: '/leaderboard',\n    label: 'Leader Board'\n  }, {\n    path: '/cards',\n    label: 'Cards'\n  }, {\n    path: '/how-to-play',\n    label: 'How to Play'\n  }, {\n    path: '/rules',\n    label: 'Rules'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"main-navigation\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"nav-container\",\n      children: navItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n        to: item.path,\n        className: `nav-item ${location.pathname === item.path ? 'active' : ''}`,\n        children: item.label\n      }, item.path, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 5\n  }, this);\n};\n\n// Main App component\n_s2(Navigation, \"vaC53idXq2T/yEyxH1p67xsNlts=\", false, function () {\n  return [useLocation, useUser];\n});\n_c4 = Navigation;\nconst AppContent = () => {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(AppRoutes, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n\n// Routes component with header\n_c5 = AppContent;\nconst AppRoutes = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"app-container\",\n    children: [/*#__PURE__*/_jsxDEV(AppHeader, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 36\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/rules\",\n        element: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"standalone-rules-page\",\n          children: /*#__PURE__*/_jsxDEV(Rules, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 80\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 41\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(UserDashboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 129,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 125,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/leaderboard\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Leaderboard, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/cards\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Cards, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/how-to-play\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(HowToPlay, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/rules\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"standalone-rules-page\",\n            children: /*#__PURE__*/_jsxDEV(Rules, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 56\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/boards/:displayName\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(BoardViewer, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 36\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      className: \"app-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Enjoy the game!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n\n// Wrap the app with the UserProvider\n_c6 = AppRoutes;\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(UserProvider, {\n    children: /*#__PURE__*/_jsxDEV(AppContent, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 188,\n    columnNumber: 5\n  }, this);\n};\n_c7 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7;\n$RefreshReg$(_c, \"ProtectedRoute\");\n$RefreshReg$(_c2, \"Rules\");\n$RefreshReg$(_c3, \"AppHeader\");\n$RefreshReg$(_c4, \"Navigation\");\n$RefreshReg$(_c5, \"AppContent\");\n$RefreshReg$(_c6, \"AppRoutes\");\n$RefreshReg$(_c7, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "useLocation", "Link", "UserProvider", "useUser", "<PERSON><PERSON>", "UserDashboard", "<PERSON><PERSON>iewer", "Leaderboard", "Cards", "HowToPlay", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "user", "loading", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "Rules", "id", "_c2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "src", "process", "env", "PUBLIC_URL", "alt", "_c3", "Navigation", "_s2", "location", "navItems", "path", "label", "map", "item", "pathname", "_c4", "A<PERSON><PERSON><PERSON>nt", "AppRoutes", "_c5", "element", "_c6", "App", "_c7", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/App.js"], "sourcesContent": ["import React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate, useLocation, Link } from 'react-router-dom';\r\nimport './App.css';\r\n\r\n// Import components\r\nimport { UserProvider, useUser } from './components/Auth/UserContext';\r\nimport Login from './components/Auth/Login';\r\nimport UserDashboard from './components/Dashboard/UserDashboard';\r\nimport BoardViewer from './components/Board/BoardViewer';\r\nimport Leaderboard from './components/Board/Leaderboard';\r\nimport Cards from './components/Cards/Cards';\r\nimport HowToPlay from './components/HowToPlay/HowToPlay';\r\n\r\n// Protected Route component\r\nconst ProtectedRoute = ({ children }) => {\r\n  const { user, loading } = useUser();\r\n\r\n  // If still loading, show loading indicator\r\n  if (loading) {\r\n    return <div className=\"loading-message\">Loading...</div>;\r\n  }\r\n\r\n  // If not logged in, redirect to login page\r\n  if (!user) {\r\n    return <Navigate to=\"/\" replace />;\r\n  }\r\n\r\n  // If logged in, render the protected component\r\n  return children;\r\n};\r\n\r\n// Rules component\r\nexport const Rules = () => {\r\n  return (\r\n    <div className=\"rules-container\">\r\n      <div id=\"rules\" className=\"rules-section\">\r\n        <h2>Rules</h2>\r\n        <p>Please follow these important guidelines for a fun and respectful experience:</p>\r\n        <ol>\r\n          <li>Cosplay is NOT consent - always ask for permission before taking photos.</li>\r\n          <li>You must be featured in the photo (at least a hand) to claim a square.</li>\r\n          <li>Teams can have as many members as you'd like, but there will still only be a single prize per team.</li>\r\n          <li>You can refresh to get a new board, but you will lose all your current progress.</li>\r\n          <li>Each player receives a unique bingo board with characters of different rarities.</li>\r\n          <li>Characters are ranked by rarity: FREE, R, SR, SSR, and UR+.</li>\r\n          <li>Higher rarity characters are worth more points, but are harder to find.</li>\r\n        </ol>\r\n        <p>Good luck, and happy hunting!</p>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\n// Header component with logo and thank you message in same row\r\nconst AppHeader = () => {\r\n  return (\r\n    <header className=\"app-header\">\r\n      <div className=\"header-row\">\r\n        <div className=\"logo-container\">\r\n          <img src={`${process.env.PUBLIC_URL}/title-logo.png`} alt=\"Bimbo Hunter Logo\" className=\"title-logo\" />\r\n        </div>\r\n        <div className=\"thank-you-message\">\r\n          <p>Thank you for competing in Official 2025 Bimbo Hunt!</p>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n};\r\n\r\n// Navigation component\r\nconst Navigation = () => {\r\n  const location = useLocation();\r\n  const { user } = useUser();\r\n\r\n  // Only show navigation for logged-in users\r\n  if (!user) return null;\r\n\r\n  const navItems = [\r\n    { path: '/dashboard', label: 'Your Board' },\r\n    { path: '/leaderboard', label: 'Leader Board' },\r\n    { path: '/cards', label: 'Cards' },\r\n    { path: '/how-to-play', label: 'How to Play' },\r\n    { path: '/rules', label: 'Rules' }\r\n  ];\r\n\r\n  return (\r\n    <nav className=\"main-navigation\">\r\n      <div className=\"nav-container\">\r\n        {navItems.map((item) => (\r\n          <Link\r\n            key={item.path}\r\n            to={item.path}\r\n            className={`nav-item ${location.pathname === item.path ? 'active' : ''}`}\r\n          >\r\n            {item.label}\r\n          </Link>\r\n        ))}\r\n      </div>\r\n    </nav>\r\n  );\r\n};\r\n\r\n// Main App component\r\nconst AppContent = () => {\r\n  return (\r\n    <Router>\r\n      <AppRoutes />\r\n    </Router>\r\n  );\r\n};\r\n\r\n// Routes component with header\r\nconst AppRoutes = () => {\r\n  return (\r\n    <div className=\"app-container\">\r\n      <AppHeader />\r\n      <Navigation />\r\n\r\n        <Routes>\r\n          {/* Public routes */}\r\n          <Route path=\"/\" element={<Login />} />\r\n          <Route path=\"/rules\" element={<div className=\"standalone-rules-page\"><Rules /></div>} />\r\n\r\n          {/* Protected routes */}\r\n          <Route\r\n            path=\"/dashboard\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <UserDashboard />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/leaderboard\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Leaderboard />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/cards\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <Cards />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/how-to-play\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <HowToPlay />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/rules\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <div className=\"standalone-rules-page\"><Rules /></div>\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n          <Route\r\n            path=\"/boards/:displayName\"\r\n            element={\r\n              <ProtectedRoute>\r\n                <BoardViewer />\r\n              </ProtectedRoute>\r\n            }\r\n          />\r\n\r\n          {/* Fallback route */}\r\n          <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\r\n        </Routes>\r\n\r\n        <footer className=\"app-footer\">\r\n          <p>Enjoy the game!</p>\r\n        </footer>\r\n      </div>\r\n  );\r\n};\r\n\r\n// Wrap the app with the UserProvider\r\nconst App = () => {\r\n  return (\r\n    <UserProvider>\r\n      <AppContent />\r\n    </UserProvider>\r\n  );\r\n};\r\n\r\nexport default App;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,IAAI,QAAQ,kBAAkB;AACtG,OAAO,WAAW;;AAElB;AACA,SAASC,YAAY,EAAEC,OAAO,QAAQ,+BAA+B;AACrE,OAAOC,KAAK,MAAM,yBAAyB;AAC3C,OAAOC,aAAa,MAAM,sCAAsC;AAChE,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,KAAK,MAAM,0BAA0B;AAC5C,OAAOC,SAAS,MAAM,kCAAkC;;AAExD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGb,OAAO,CAAC,CAAC;;EAEnC;EACA,IAAIa,OAAO,EAAE;IACX,oBAAOL,OAAA;MAAKM,SAAS,EAAC,iBAAiB;MAAAJ,QAAA,EAAC;IAAU;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAC1D;;EAEA;EACA,IAAI,CAACN,IAAI,EAAE;IACT,oBAAOJ,OAAA,CAACZ,QAAQ;MAACuB,EAAE,EAAC,GAAG;MAACC,OAAO;IAAA;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EACpC;;EAEA;EACA,OAAOR,QAAQ;AACjB,CAAC;;AAED;AAAAC,EAAA,CAjBMF,cAAc;EAAA,QACQT,OAAO;AAAA;AAAAqB,EAAA,GAD7BZ,cAAc;AAkBpB,OAAO,MAAMa,KAAK,GAAGA,CAAA,KAAM;EACzB,oBACEd,OAAA;IAAKM,SAAS,EAAC,iBAAiB;IAAAJ,QAAA,eAC9BF,OAAA;MAAKe,EAAE,EAAC,OAAO;MAACT,SAAS,EAAC,eAAe;MAAAJ,QAAA,gBACvCF,OAAA;QAAAE,QAAA,EAAI;MAAK;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACdV,OAAA;QAAAE,QAAA,EAAG;MAA6E;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACpFV,OAAA;QAAAE,QAAA,gBACEF,OAAA;UAAAE,QAAA,EAAI;QAAwE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjFV,OAAA;UAAAE,QAAA,EAAI;QAAsE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/EV,OAAA;UAAAE,QAAA,EAAI;QAAmG;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5GV,OAAA;UAAAE,QAAA,EAAI;QAAgF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzFV,OAAA;UAAAE,QAAA,EAAI;QAAgF;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzFV,OAAA;UAAAE,QAAA,EAAI;QAA2D;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpEV,OAAA;UAAAE,QAAA,EAAI;QAAuE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,eACLV,OAAA;QAAAE,QAAA,EAAG;MAA6B;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAM,GAAA,GArBaF,KAAK;AAsBlB,MAAMG,SAAS,GAAGA,CAAA,KAAM;EACtB,oBACEjB,OAAA;IAAQM,SAAS,EAAC,YAAY;IAAAJ,QAAA,eAC5BF,OAAA;MAAKM,SAAS,EAAC,YAAY;MAAAJ,QAAA,gBACzBF,OAAA;QAAKM,SAAS,EAAC,gBAAgB;QAAAJ,QAAA,eAC7BF,OAAA;UAAKkB,GAAG,EAAE,GAAGC,OAAO,CAACC,GAAG,CAACC,UAAU,iBAAkB;UAACC,GAAG,EAAC,mBAAmB;UAAChB,SAAS,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpG,CAAC,eACNV,OAAA;QAAKM,SAAS,EAAC,mBAAmB;QAAAJ,QAAA,eAChCF,OAAA;UAAAE,QAAA,EAAG;QAAoD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;;AAED;AAAAa,GAAA,GAfMN,SAAS;AAgBf,MAAMO,UAAU,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvB,MAAMC,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe;EAAK,CAAC,GAAGZ,OAAO,CAAC,CAAC;;EAE1B;EACA,IAAI,CAACY,IAAI,EAAE,OAAO,IAAI;EAEtB,MAAMuB,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAa,CAAC,EAC3C;IAAED,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAe,CAAC,EAC/C;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAED,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAE;EAAc,CAAC,EAC9C;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAC,CACnC;EAED,oBACE7B,OAAA;IAAKM,SAAS,EAAC,iBAAiB;IAAAJ,QAAA,eAC9BF,OAAA;MAAKM,SAAS,EAAC,eAAe;MAAAJ,QAAA,EAC3ByB,QAAQ,CAACG,GAAG,CAAEC,IAAI,iBACjB/B,OAAA,CAACV,IAAI;QAEHqB,EAAE,EAAEoB,IAAI,CAACH,IAAK;QACdtB,SAAS,EAAE,YAAYoB,QAAQ,CAACM,QAAQ,KAAKD,IAAI,CAACH,IAAI,GAAG,QAAQ,GAAG,EAAE,EAAG;QAAA1B,QAAA,EAExE6B,IAAI,CAACF;MAAK,GAJNE,IAAI,CAACH,IAAI;QAAArB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAKV,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAe,GAAA,CAhCMD,UAAU;EAAA,QACGnC,WAAW,EACXG,OAAO;AAAA;AAAAyC,GAAA,GAFpBT,UAAU;AAiChB,MAAMU,UAAU,GAAGA,CAAA,KAAM;EACvB,oBACElC,OAAA,CAACf,MAAM;IAAAiB,QAAA,eACLF,OAAA,CAACmC,SAAS;MAAA5B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEb,CAAC;;AAED;AAAA0B,GAAA,GARMF,UAAU;AAShB,MAAMC,SAAS,GAAGA,CAAA,KAAM;EACtB,oBACEnC,OAAA;IAAKM,SAAS,EAAC,eAAe;IAAAJ,QAAA,gBAC5BF,OAAA,CAACiB,SAAS;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACbV,OAAA,CAACwB,UAAU;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEZV,OAAA,CAACd,MAAM;MAAAgB,QAAA,gBAELF,OAAA,CAACb,KAAK;QAACyC,IAAI,EAAC,GAAG;QAACS,OAAO,eAAErC,OAAA,CAACP,KAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtCV,OAAA,CAACb,KAAK;QAACyC,IAAI,EAAC,QAAQ;QAACS,OAAO,eAAErC,OAAA;UAAKM,SAAS,EAAC,uBAAuB;UAAAJ,QAAA,eAACF,OAAA,CAACc,KAAK;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGxFV,OAAA,CAACb,KAAK;QACJyC,IAAI,EAAC,YAAY;QACjBS,OAAO,eACLrC,OAAA,CAACC,cAAc;UAAAC,QAAA,eACbF,OAAA,CAACN,aAAa;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFV,OAAA,CAACb,KAAK;QACJyC,IAAI,EAAC,cAAc;QACnBS,OAAO,eACLrC,OAAA,CAACC,cAAc;UAAAC,QAAA,eACbF,OAAA,CAACJ,WAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFV,OAAA,CAACb,KAAK;QACJyC,IAAI,EAAC,QAAQ;QACbS,OAAO,eACLrC,OAAA,CAACC,cAAc;UAAAC,QAAA,eACbF,OAAA,CAACH,KAAK;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFV,OAAA,CAACb,KAAK;QACJyC,IAAI,EAAC,cAAc;QACnBS,OAAO,eACLrC,OAAA,CAACC,cAAc;UAAAC,QAAA,eACbF,OAAA,CAACF,SAAS;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFV,OAAA,CAACb,KAAK;QACJyC,IAAI,EAAC,QAAQ;QACbS,OAAO,eACLrC,OAAA,CAACC,cAAc;UAAAC,QAAA,eACbF,OAAA;YAAKM,SAAS,EAAC,uBAAuB;YAAAJ,QAAA,eAACF,OAAA,CAACc,KAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eACFV,OAAA,CAACb,KAAK;QACJyC,IAAI,EAAC,sBAAsB;QAC3BS,OAAO,eACLrC,OAAA,CAACC,cAAc;UAAAC,QAAA,eACbF,OAAA,CAACL,WAAW;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGFV,OAAA,CAACb,KAAK;QAACyC,IAAI,EAAC,GAAG;QAACS,OAAO,eAAErC,OAAA,CAACZ,QAAQ;UAACuB,EAAE,EAAC,GAAG;UAACC,OAAO;QAAA;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAETV,OAAA;MAAQM,SAAS,EAAC,YAAY;MAAAJ,QAAA,eAC5BF,OAAA;QAAAE,QAAA,EAAG;MAAe;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEZ,CAAC;;AAED;AAAA4B,GAAA,GAxEMH,SAAS;AAyEf,MAAMI,GAAG,GAAGA,CAAA,KAAM;EAChB,oBACEvC,OAAA,CAACT,YAAY;IAAAW,QAAA,eACXF,OAAA,CAACkC,UAAU;MAAA3B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEnB,CAAC;AAAC8B,GAAA,GANID,GAAG;AAQT,eAAeA,GAAG;AAAC,IAAA1B,EAAA,EAAAG,GAAA,EAAAO,GAAA,EAAAU,GAAA,EAAAG,GAAA,EAAAE,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAA5B,EAAA;AAAA4B,YAAA,CAAAzB,GAAA;AAAAyB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAR,GAAA;AAAAQ,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}