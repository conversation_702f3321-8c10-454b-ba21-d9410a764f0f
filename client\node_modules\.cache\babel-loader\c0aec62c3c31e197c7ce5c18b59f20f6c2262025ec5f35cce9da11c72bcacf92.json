{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\Projects\\\\bimbo-hunter-base\\\\client\\\\src\\\\components\\\\Auth\\\\UserContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useState, useEffect, useContext } from 'react';\n\n// Create the user context\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserContext = /*#__PURE__*/createContext();\n\n// Custom hook to use the user context\nexport const useUser = () => {\n  _s();\n  return useContext(UserContext);\n};\n\n// User provider component\n_s(useUser, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport const UserProvider = ({\n  children\n}) => {\n  _s2();\n  // State for user data\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Check for existing user session on mount\n  useEffect(() => {\n    const checkUserSession = async () => {\n      try {\n        // Check if user data exists in local storage\n        const storedUser = localStorage.getItem('user');\n        if (storedUser) {\n          const userData = JSON.parse(storedUser);\n\n          // Verify the user still exists in the database\n          const response = await fetch(`http://localhost:5000/api/auth/login`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n              pin: userData.pin\n            })\n          });\n          if (response.ok) {\n            const data = await response.json();\n            if (data.success) {\n              setUser(data.user);\n            } else {\n              // Clear invalid session\n              localStorage.removeItem('user');\n              setUser(null);\n            }\n          } else {\n            // Clear invalid session\n            localStorage.removeItem('user');\n            setUser(null);\n          }\n        }\n      } catch (err) {\n        console.error('Error checking user session:', err);\n        setError('Failed to restore user session');\n      } finally {\n        setLoading(false);\n      }\n    };\n    checkUserSession();\n  }, []);\n\n  // Login function\n  const login = async pin => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await fetch('http://localhost:5000/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          pin\n        })\n      });\n      const data = await response.json();\n      if (response.ok && data.success) {\n        // Save user data to state and local storage\n        setUser(data.user);\n        localStorage.setItem('user', JSON.stringify(data.user));\n        return {\n          success: true\n        };\n      } else {\n        setError(data.error || 'Invalid PIN');\n        return {\n          success: false,\n          error: data.error || 'Invalid PIN'\n        };\n      }\n    } catch (err) {\n      console.error('Login error:', err);\n      setError('Failed to login. Please try again.');\n      return {\n        success: false,\n        error: 'Failed to login. Please try again.'\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Register function\n  const register = async (pin, displayName) => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await fetch('http://localhost:5000/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          pin,\n          display_name: displayName\n        })\n      });\n      const data = await response.json();\n      if (response.ok && data.success) {\n        // Save user data to state and local storage\n        setUser(data.user);\n        localStorage.setItem('user', JSON.stringify(data.user));\n        return {\n          success: true\n        };\n      } else {\n        setError(data.error || 'Failed to register');\n        return {\n          success: false,\n          error: data.error || 'Failed to register'\n        };\n      }\n    } catch (err) {\n      console.error('Registration error:', err);\n      setError('Failed to register. Please try again.');\n      return {\n        success: false,\n        error: 'Failed to register. Please try again.'\n      };\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Logout function\n  const logout = () => {\n    localStorage.removeItem('user');\n    setUser(null);\n  };\n\n  // Context value\n  const value = {\n    user,\n    loading,\n    error,\n    login,\n    register,\n    logout\n  };\n  return /*#__PURE__*/_jsxDEV(UserContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 145,\n    columnNumber: 10\n  }, this);\n};\n_s2(UserProvider, \"PA9FxEY9xSNRrsSqaLtbYei52Hs=\");\n_c = UserProvider;\nexport default UserContext;\nvar _c;\n$RefreshReg$(_c, \"UserProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useState", "useEffect", "useContext", "jsxDEV", "_jsxDEV", "UserContext", "useUser", "_s", "UserProvider", "children", "_s2", "user", "setUser", "loading", "setLoading", "error", "setError", "checkUserSession", "storedUser", "localStorage", "getItem", "userData", "JSON", "parse", "response", "fetch", "method", "headers", "body", "stringify", "pin", "ok", "data", "json", "success", "removeItem", "err", "console", "login", "setItem", "register", "displayName", "display_name", "logout", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/Projects/bimbo-hunter-base/client/src/components/Auth/UserContext.js"], "sourcesContent": ["import React, { createContext, useState, useEffect, useContext } from 'react';\r\n\r\n// Create the user context\r\nconst UserContext = createContext();\r\n\r\n// Custom hook to use the user context\r\nexport const useUser = () => useContext(UserContext);\r\n\r\n// User provider component\r\nexport const UserProvider = ({ children }) => {\r\n  // State for user data\r\n  const [user, setUser] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  // Check for existing user session on mount\r\n  useEffect(() => {\r\n    const checkUserSession = async () => {\r\n      try {\r\n        // Check if user data exists in local storage\r\n        const storedUser = localStorage.getItem('user');\r\n        \r\n        if (storedUser) {\r\n          const userData = JSON.parse(storedUser);\r\n          \r\n          // Verify the user still exists in the database\r\n          const response = await fetch(`http://localhost:5000/api/auth/login`, {\r\n            method: 'POST',\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n            body: JSON.stringify({ pin: userData.pin }),\r\n          });\r\n          \r\n          if (response.ok) {\r\n            const data = await response.json();\r\n            if (data.success) {\r\n              setUser(data.user);\r\n            } else {\r\n              // Clear invalid session\r\n              localStorage.removeItem('user');\r\n              setUser(null);\r\n            }\r\n          } else {\r\n            // Clear invalid session\r\n            localStorage.removeItem('user');\r\n            setUser(null);\r\n          }\r\n        }\r\n      } catch (err) {\r\n        console.error('Error checking user session:', err);\r\n        setError('Failed to restore user session');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    checkUserSession();\r\n  }, []);\r\n\r\n  // Login function\r\n  const login = async (pin) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const response = await fetch('http://localhost:5000/api/auth/login', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ pin }),\r\n      });\r\n      \r\n      const data = await response.json();\r\n      \r\n      if (response.ok && data.success) {\r\n        // Save user data to state and local storage\r\n        setUser(data.user);\r\n        localStorage.setItem('user', JSON.stringify(data.user));\r\n        return { success: true };\r\n      } else {\r\n        setError(data.error || 'Invalid PIN');\r\n        return { success: false, error: data.error || 'Invalid PIN' };\r\n      }\r\n    } catch (err) {\r\n      console.error('Login error:', err);\r\n      setError('Failed to login. Please try again.');\r\n      return { success: false, error: 'Failed to login. Please try again.' };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Register function\r\n  const register = async (pin, displayName) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      \r\n      const response = await fetch('http://localhost:5000/api/auth/register', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({ pin, display_name: displayName }),\r\n      });\r\n      \r\n      const data = await response.json();\r\n      \r\n      if (response.ok && data.success) {\r\n        // Save user data to state and local storage\r\n        setUser(data.user);\r\n        localStorage.setItem('user', JSON.stringify(data.user));\r\n        return { success: true };\r\n      } else {\r\n        setError(data.error || 'Failed to register');\r\n        return { success: false, error: data.error || 'Failed to register' };\r\n      }\r\n    } catch (err) {\r\n      console.error('Registration error:', err);\r\n      setError('Failed to register. Please try again.');\r\n      return { success: false, error: 'Failed to register. Please try again.' };\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Logout function\r\n  const logout = () => {\r\n    localStorage.removeItem('user');\r\n    setUser(null);\r\n  };\r\n\r\n  // Context value\r\n  const value = {\r\n    user,\r\n    loading,\r\n    error,\r\n    login,\r\n    register,\r\n    logout,\r\n  };\r\n\r\n  return <UserContext.Provider value={value}>{children}</UserContext.Provider>;\r\n};\r\n\r\nexport default UserContext;\r\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,UAAU,QAAQ,OAAO;;AAE7E;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,gBAAGN,aAAa,CAAC,CAAC;;AAEnC;AACA,OAAO,MAAMO,OAAO,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAML,UAAU,CAACG,WAAW,CAAC;AAAA;;AAEpD;AAAAE,EAAA,CAFaD,OAAO;AAGpB,OAAO,MAAME,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C;EACA,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACa,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACAC,SAAS,CAAC,MAAM;IACd,MAAMgB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF;QACA,MAAMC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;QAE/C,IAAIF,UAAU,EAAE;UACd,MAAMG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;;UAEvC;UACA,MAAMM,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC,EAAE;YACnEC,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAEN,IAAI,CAACO,SAAS,CAAC;cAAEC,GAAG,EAAET,QAAQ,CAACS;YAAI,CAAC;UAC5C,CAAC,CAAC;UAEF,IAAIN,QAAQ,CAACO,EAAE,EAAE;YACf,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;YAClC,IAAID,IAAI,CAACE,OAAO,EAAE;cAChBtB,OAAO,CAACoB,IAAI,CAACrB,IAAI,CAAC;YACpB,CAAC,MAAM;cACL;cACAQ,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;cAC/BvB,OAAO,CAAC,IAAI,CAAC;YACf;UACF,CAAC,MAAM;YACL;YACAO,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;YAC/BvB,OAAO,CAAC,IAAI,CAAC;UACf;QACF;MACF,CAAC,CAAC,OAAOwB,GAAG,EAAE;QACZC,OAAO,CAACtB,KAAK,CAAC,8BAA8B,EAAEqB,GAAG,CAAC;QAClDpB,QAAQ,CAAC,gCAAgC,CAAC;MAC5C,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMqB,KAAK,GAAG,MAAOR,GAAG,IAAK;IAC3B,IAAI;MACFhB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,sCAAsC,EAAE;QACnEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEN,IAAI,CAACO,SAAS,CAAC;UAAEC;QAAI,CAAC;MAC9B,CAAC,CAAC;MAEF,MAAME,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAIT,QAAQ,CAACO,EAAE,IAAIC,IAAI,CAACE,OAAO,EAAE;QAC/B;QACAtB,OAAO,CAACoB,IAAI,CAACrB,IAAI,CAAC;QAClBQ,YAAY,CAACoB,OAAO,CAAC,MAAM,EAAEjB,IAAI,CAACO,SAAS,CAACG,IAAI,CAACrB,IAAI,CAAC,CAAC;QACvD,OAAO;UAAEuB,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACLlB,QAAQ,CAACgB,IAAI,CAACjB,KAAK,IAAI,aAAa,CAAC;QACrC,OAAO;UAAEmB,OAAO,EAAE,KAAK;UAAEnB,KAAK,EAAEiB,IAAI,CAACjB,KAAK,IAAI;QAAc,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZC,OAAO,CAACtB,KAAK,CAAC,cAAc,EAAEqB,GAAG,CAAC;MAClCpB,QAAQ,CAAC,oCAAoC,CAAC;MAC9C,OAAO;QAAEkB,OAAO,EAAE,KAAK;QAAEnB,KAAK,EAAE;MAAqC,CAAC;IACxE,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM0B,QAAQ,GAAG,MAAAA,CAAOV,GAAG,EAAEW,WAAW,KAAK;IAC3C,IAAI;MACF3B,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMQ,QAAQ,GAAG,MAAMC,KAAK,CAAC,yCAAyC,EAAE;QACtEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEN,IAAI,CAACO,SAAS,CAAC;UAAEC,GAAG;UAAEY,YAAY,EAAED;QAAY,CAAC;MACzD,CAAC,CAAC;MAEF,MAAMT,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAIT,QAAQ,CAACO,EAAE,IAAIC,IAAI,CAACE,OAAO,EAAE;QAC/B;QACAtB,OAAO,CAACoB,IAAI,CAACrB,IAAI,CAAC;QAClBQ,YAAY,CAACoB,OAAO,CAAC,MAAM,EAAEjB,IAAI,CAACO,SAAS,CAACG,IAAI,CAACrB,IAAI,CAAC,CAAC;QACvD,OAAO;UAAEuB,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC,MAAM;QACLlB,QAAQ,CAACgB,IAAI,CAACjB,KAAK,IAAI,oBAAoB,CAAC;QAC5C,OAAO;UAAEmB,OAAO,EAAE,KAAK;UAAEnB,KAAK,EAAEiB,IAAI,CAACjB,KAAK,IAAI;QAAqB,CAAC;MACtE;IACF,CAAC,CAAC,OAAOqB,GAAG,EAAE;MACZC,OAAO,CAACtB,KAAK,CAAC,qBAAqB,EAAEqB,GAAG,CAAC;MACzCpB,QAAQ,CAAC,uCAAuC,CAAC;MACjD,OAAO;QAAEkB,OAAO,EAAE,KAAK;QAAEnB,KAAK,EAAE;MAAwC,CAAC;IAC3E,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6B,MAAM,GAAGA,CAAA,KAAM;IACnBxB,YAAY,CAACgB,UAAU,CAAC,MAAM,CAAC;IAC/BvB,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;;EAED;EACA,MAAMgC,KAAK,GAAG;IACZjC,IAAI;IACJE,OAAO;IACPE,KAAK;IACLuB,KAAK;IACLE,QAAQ;IACRG;EACF,CAAC;EAED,oBAAOvC,OAAA,CAACC,WAAW,CAACwC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAnC,QAAA,EAAEA;EAAQ;IAAAqC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACvC,GAAA,CAxIWF,YAAY;AAAA0C,EAAA,GAAZ1C,YAAY;AA0IzB,eAAeH,WAAW;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}