{"_from": "@testing-library/user-event@^13.5.0", "_id": "@testing-library/user-event@13.5.0", "_inBundle": false, "_integrity": "sha512-5Kwtbo3Y/NowpkbRuSepbyMFkZmHgD+vPzYB/RJ4oxt5Gj/avFFBYjhw27cqSVPVw/3a67NK1PbiIr9k4Gwmdg==", "_location": "/@testing-library/user-event", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@testing-library/user-event@^13.5.0", "name": "@testing-library/user-event", "escapedName": "@testing-library%2fuser-event", "scope": "@testing-library", "rawSpec": "^13.5.0", "saveSpec": null, "fetchSpec": "^13.5.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@testing-library/user-event/-/user-event-13.5.0.tgz", "_shasum": "69d77007f1e124d55314a2b73fd204b333b13295", "_spec": "@testing-library/user-event@^13.5.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/testing-library/user-event/issues"}, "bundleDependencies": false, "dependencies": {"@babel/runtime": "^7.12.5"}, "deprecated": false, "description": "Fire events the same way the user does", "devDependencies": {"@testing-library/dom": "^7.28.1", "@testing-library/jest-dom": "^5.11.6", "@testing-library/react": "^11.2.5", "@types/estree": "0.0.45", "@types/jest-in-case": "^1.0.3", "@types/react": "^17.0.3", "is-ci": "^2.0.0", "jest-in-case": "^1.0.2", "jest-serializer-ansi": "^1.0.3", "kcd-scripts": "^11.1.0", "react": "^17.0.2", "react-dom": "^17.0.2", "typescript": "^4.1.2"}, "engines": {"node": ">=10", "npm": ">=6"}, "eslintConfig": {"extends": "./node_modules/kcd-scripts/eslint.js", "rules": {"jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/tabindex-no-positive": "off", "no-func-assign": "off", "no-return-assign": "off", "react/prop-types": "off", "testing-library/no-dom-import": "off"}, "overrides": [{"files": ["**/__tests__/**"], "rules": {"no-console": "off"}}]}, "eslintIgnore": ["node_modules", "coverage", "dist"], "files": ["dist"], "homepage": "https://github.com/testing-library/user-event#readme", "keywords": ["react-testing-library", "dom-testing-library", "react", "testing"], "license": "MIT", "main": "dist/index.js", "name": "@testing-library/user-event", "peerDependencies": {"@testing-library/dom": ">=7.21.4"}, "repository": {"type": "git", "url": "git+https://github.com/testing-library/user-event.git"}, "scripts": {"build": "kcd-scripts build", "lint": "kcd-scripts lint", "setup": "npm install && npm run validate -s", "test": "kcd-scripts test", "test:debug": "kcd-scripts --inspect-brk test --runInBand", "test:update": "npm test -- --updateSnapshot --coverage", "typecheck": "kcd-scripts typecheck", "validate": "kcd-scripts validate"}, "version": "13.5.0"}