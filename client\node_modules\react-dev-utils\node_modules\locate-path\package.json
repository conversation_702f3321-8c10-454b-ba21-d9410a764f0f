{"_from": "locate-path@^6.0.0", "_id": "locate-path@6.0.0", "_inBundle": false, "_integrity": "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==", "_location": "/react-dev-utils/locate-path", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "locate-path@^6.0.0", "name": "locate-path", "escapedName": "locate-path", "rawSpec": "^6.0.0", "saveSpec": null, "fetchSpec": "^6.0.0"}, "_requiredBy": ["/react-dev-utils/find-up"], "_resolved": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz", "_shasum": "55321eb309febbc59c4801d931a72452a681d286", "_spec": "locate-path@^6.0.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client\\node_modules\\react-dev-utils\\node_modules\\find-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/locate-path/issues"}, "bundleDependencies": false, "dependencies": {"p-locate": "^5.0.0"}, "deprecated": false, "description": "Get the first path that exists on disk of multiple paths", "devDependencies": {"ava": "^2.4.0", "tsd": "^0.13.1", "xo": "^0.32.1"}, "engines": {"node": ">=10"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/locate-path#readme", "keywords": ["locate", "path", "paths", "file", "files", "exists", "find", "finder", "search", "searcher", "array", "iterable", "iterator"], "license": "MIT", "name": "locate-path", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/locate-path.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "6.0.0"}