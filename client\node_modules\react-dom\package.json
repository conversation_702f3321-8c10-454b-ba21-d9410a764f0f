{"_from": "react-dom@^19.1.0", "_id": "react-dom@19.1.0", "_inBundle": false, "_integrity": "sha512-Xs1hdnE+DyKgeHJeJznQmYMIBG3TKIHJJT95Q58nHLSrElKlGQqDTR2HQ9fx5CN/Gk6Vh/kupBTDLU11/nDk/g==", "_location": "/react-dom", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "react-dom@^19.1.0", "name": "react-dom", "escapedName": "react-dom", "rawSpec": "^19.1.0", "saveSpec": null, "fetchSpec": "^19.1.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/react-dom/-/react-dom-19.1.0.tgz", "_shasum": "133558deca37fa1d682708df8904b25186793623", "_spec": "react-dom@^19.1.0", "_where": "C:\\Users\\<USER>\\Documents\\Projects\\bimbo-hunter-base\\client", "browser": {"./server.js": "./server.browser.js", "./static.js": "./static.browser.js"}, "bugs": {"url": "https://github.com/facebook/react/issues"}, "bundleDependencies": false, "dependencies": {"scheduler": "^0.26.0"}, "deprecated": false, "description": "React package for working with the DOM.", "exports": {".": {"react-server": "./react-dom.react-server.js", "default": "./index.js"}, "./client": {"react-server": "./client.react-server.js", "default": "./client.js"}, "./server": {"react-server": "./server.react-server.js", "workerd": "./server.edge.js", "bun": "./server.bun.js", "deno": "./server.browser.js", "worker": "./server.browser.js", "node": "./server.node.js", "edge-light": "./server.edge.js", "browser": "./server.browser.js", "default": "./server.node.js"}, "./server.browser": {"react-server": "./server.react-server.js", "default": "./server.browser.js"}, "./server.bun": {"react-server": "./server.react-server.js", "default": "./server.bun.js"}, "./server.edge": {"react-server": "./server.react-server.js", "default": "./server.edge.js"}, "./server.node": {"react-server": "./server.react-server.js", "default": "./server.node.js"}, "./static": {"react-server": "./static.react-server.js", "workerd": "./static.edge.js", "deno": "./static.browser.js", "worker": "./static.browser.js", "node": "./static.node.js", "edge-light": "./static.edge.js", "browser": "./static.browser.js", "default": "./static.node.js"}, "./static.browser": {"react-server": "./static.react-server.js", "default": "./static.browser.js"}, "./static.edge": {"react-server": "./static.react-server.js", "default": "./static.edge.js"}, "./static.node": {"react-server": "./static.react-server.js", "default": "./static.node.js"}, "./profiling": {"react-server": "./profiling.react-server.js", "default": "./profiling.js"}, "./test-utils": "./test-utils.js", "./package.json": "./package.json"}, "files": ["LICENSE", "README.md", "client.js", "client.react-server.js", "index.js", "profiling.js", "profiling.react-server.js", "react-dom.react-server.js", "server.browser.js", "server.bun.js", "server.edge.js", "server.js", "server.node.js", "server.react-server.js", "static.browser.js", "static.edge.js", "static.js", "static.node.js", "static.react-server.js", "test-utils.js", "cjs/"], "homepage": "https://react.dev/", "keywords": ["react"], "license": "MIT", "main": "index.js", "name": "react-dom", "peerDependencies": {"react": "^19.1.0"}, "repository": {"type": "git", "url": "git+https://github.com/facebook/react.git", "directory": "packages/react-dom"}, "scripts": {"start": "node server.js"}, "version": "19.1.0"}